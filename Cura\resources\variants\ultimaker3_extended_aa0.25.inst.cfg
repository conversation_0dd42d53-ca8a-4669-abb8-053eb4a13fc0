[general]
definition = ultimaker3_extended
name = AA 0.25
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
brim_width = 7
cool_min_layer_time = 0
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
machine_nozzle_id = AA 0.25
machine_nozzle_size = 0.25
machine_nozzle_tip_outer_diameter = 0.65
retraction_min_travel = 0.7
retraction_prime_speed = =retraction_speed
skin_overlap = 15
speed_print = 55
speed_topbottom = 20
speed_wall = =math.ceil(speed_print * 30 / 55)
support_angle = 60
support_bottom_distance = =support_z_distance / 2
support_top_distance = =support_z_distance
support_z_distance = =layer_height * 2
switch_extruder_prime_speed = =switch_extruder_retraction_speeds
switch_extruder_retraction_amount = =machine_heat_zone_length
top_bottom_thickness = 1.2
wall_thickness = =wall_line_width_0 + wall_line_width_x * 2
xy_offset_layer_0 = =(-0.2 if adhesion_type == "skirt" or adhesion_type == "none" else 0) + xy_offset

