
<html lang="en">
    <head>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.9/standalone/umd/vis-network.min.js" integrity="sha512-iTgTmIgxyA2YehKNVbzLJx4j9SnuC5ihtRrtxVkXH/9nF3vXBN5YeNQp+6wufBWKD3u+roHVNOvWBMufQnBbug==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    </head>

    <body>
        <style>
            @media print {
                .noPrint {
                    display: none;
                }
            }
            .button {
                background-color: #5555cc;
                border: none;
                color: white;
                padding: 5px 10px;
                text-align: center;
                text-decoration: none;
                display: inline-block;
                font-size: 18px;
            }
        </style>

        <div style="display: grid; grid-template-columns: 75% 25%; grid-template-rows: 30px auto; height: 100vh;">
            <div id="mylegend" style="background-color: lightgrey; grid-column-end: span 2;"></div>
            <div id="mynetwork"></div>
            <div style="background-color: lightgrey;min-height:100%;height:0;overflow-y: auto;">
                <div>
                    <input type="checkbox" onchange="switchBuild()" id="show_build_requires" checked />
                    <label for="show_build_requires">Show build-requires</label>
                </div>
                <div>
                    <input type="checkbox" onchange="switchTest()" id="show_test_requires" checked />
                    <label for="show_test_requires">Show test-requires</label>
                </div>
                <div>
                    <input type="checkbox" onchange="collapsePackages()" id="collapse_packages"/>
                    <label for="collapse_packages">Group packages</label>
                </div>
                 <div>
                    <input type="checkbox" onchange="showPackageType()" id="show_package_type"/>
                    <label for="show_package_type">Show package type</label>
                </div>
                 <div>
                    <input type="search" placeholder="Search packages..." oninput="searchPackages(this)">
                </div>
                 <div>
                    <input type="search" placeholder="Exclude packages..." title="Add a comma to exclude an additional package" oninput="excludePackages(this)">
                </div>
                <div>
                    <input type="checkbox" onchange="showhideclass('controls')" id="show_controls"/>
                    <label for="show_controls">Show graph controls</label>
                </div>
                <div id="controls" class="controls" style="padding:5; display:none"></div>
                <div id="details"  style="padding:10;" class="noPrint">Package info: Click on one package to show information</div>
                <div id="error" style="padding:10;" class="noPrint"></div>
            </div>
        </div>

        <script type="text/javascript">
            const graph_data = {"error": null, "nodes": {"0": {"author": "UltiMaker", "binary": null, "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": "missing", "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["site-packages\\cura"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": ["resources", "plugins", "packaging"], "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"cloud_api_version": "1", "cura_debug_mode": false, "display_name": "UltiMaker Cura", "enterprise": false, "i18n_extract": false, "internal": false, "skip_licenses_download": false, "staging": false}, "dependencies": {"1": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "gettext/0.22.5", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "100": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "dulcificum/5.10.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "101": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "semver_mode", "ref": "nlohmann_json/3.11.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "102": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "semver_mode", "ref": "ctre/3.7.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "103": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "pybind11/2.11.1", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "105": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "pysavitar/5.11.0-alpha.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "106": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "savitar/5.11.0-alpha.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "107": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "pugixml/1.14", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "111": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "pynest2d/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "112": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "nest2d/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "113": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "nlopt/2.7.1", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "19": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "cura_resources/5.11.0-alpha.0@ultimaker/testing", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "20": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "semver_mode", "ref": "uranium/5.11.0-alpha.0@ultimaker/testing", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "39": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "pyarcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "40": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "arcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "41": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "protobuf/3.21.12", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "zlib/1.3.1", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "openssl/3.5.0", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "expat/2.7.1", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "libffi/3.4.4", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mpdecimal/2.5.1", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "bzip2/1.0.8", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "sqlite3/3.45.2", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "xz_utils/5.4.5", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "72": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "curaengine/5.11.0-alpha.0@ultimaker/testing", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "73": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "scripta/1.1.0-alpha.0+b96045@ultimaker/testing", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "75": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "neargye-semver/0.3.0", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "76": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "curaengine_grpc_definitions/0.3.2", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "77": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "asio-grpc/2.9.2", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "78": {"build": false, "direct": false, "force": true, "headers": false, "libs": false, "package_id_mode": null, "ref": "grpc/1.54.3", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "79": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "abseil/20230802.1", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "80": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "c-ares/1.34.5", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "81": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "re2/20230301", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "85": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "boost/1.86.0", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "88": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "clipper/6.4.2@ultimaker/stable", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "89": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "rapidjson/cci.20230929", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "90": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "stb/cci.20230920", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "91": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "spdlog/1.15.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "92": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "fmt/11.1.3", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "93": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "semver_mode", "ref": "range-v3/0.12.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "94": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mapbox-wagyu/0.5.0@ultimaker/stable", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "95": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mapbox-geometry/2.0.3", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "96": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mapbox-variant/1.2.0", "run": false, "skip": true, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "98": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "cura_binary_data/5.11.0-alpha.0@ultimaker/testing", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "99": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "semver_mode", "ref": "fdm_materials/5.11.0-alpha.0@ultimaker/testing", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "3D printer / slicing GUI built on top of the Uranium framework", "generators": ["VirtualPythonEnv"], "generators_folder": null, "homepage": null, "id": "0", "immutable_package_folder": null, "info": {"options": {"cloud_api_version": "1", "cura_debug_mode": "False", "display_name": "UltiMaker Cura", "enterprise": "False", "internal": "False", "skip_licenses_download": "False", "staging": "False"}, "python_requires": ["translationextractor/2.3.Z"], "requires": ["cura_resources/5.Y.Z@ultimaker/testing", "uranium/5.Y.Z@ultimaker/testing", "pyarcus/5.Y.Z", "cura_binary_data/5.Y.Z@ultimaker/testing", "fdm_materials/5.Y.Z@ultimaker/testing", "dulcificum/5.Y.Z", "nlohmann_json/3.Y.Z", "range-v3/0.12.0", "spdlog/1.Y.Z", "fmt/11.Y.Z", "ctre/3.Y.Z", "pysavitar/5.Y.Z", "pynest2d/5.Y.Z", "cpython/3.Y.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "conanfile.py (cura/5.11.0-alpha.0)", "languages": [], "license": "LGPL-3.0", "name": "cura", "options": {"cloud_api_version": "1", "cura_debug_mode": "False", "display_name": "UltiMaker Cura", "enterprise": "False", "i18n_extract": "False", "internal": "False", "skip_licenses_download": "False", "staging": "False"}, "options_definitions": {"cloud_api_version": ["ANY"], "cura_debug_mode": ["True", "False"], "display_name": ["ANY"], "enterprise": ["True", "False"], "i18n_extract": ["True", "False"], "internal": ["True", "False"], "skip_licenses_download": ["True", "False"], "staging": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "4282b41aa89ca45db674c328a0c22a2887ae9450", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"translationextractor/2.3.0#d504876a4742c1b92bcd6e1d5ba7509a": {"path": "C:\\Users\\<USER>\\.conan2\\p\\transee40619501988\\e", "recipe": "Cache", "remote": null}}, "recipe": "Consumer", "recipe_folder": "C:\\Mac\\Home\\Desktop\\Cura-Dev\\Cura", "ref": "cura/5.11.0-alpha.0", "remote": null, "revision_mode": "hash", "rrev": null, "rrev_timestamp": null, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "python", "pyqt6", "qt", "qml", "3d-printing", "slicer"], "upload_policy": null, "url": "https://github.com/Ultimaker/cura", "user": null, "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "1": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"2": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "libiconv/1.17", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "4": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "5": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "automake/1.16.5", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "6": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "7": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "An internationalization and localization system for multilingual programs", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/gettext", "id": "1", "immutable_package_folder": null, "info": {"requires": ["libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59:ea5dc84deb25b32a77ec23c39a1811e3f441ee54"], "settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "gettext/0.22.5", "languages": [], "license": "GPL-3.0-or-later", "name": "gettext", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "f2629ff2b2a994555c09350ded2dc0c20d6e267a", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\gettee0511198a1073\\e", "ref": "gettext/0.22.5#a1f31cc77dee0345699745ef39686dd0", "remote": null, "revision_mode": "hash", "rrev": "a1f31cc77dee0345699745ef39686dd0", "rrev_timestamp": **********.89, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["intl", "libintl", "i18n"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "0.22.5", "win_bash": true, "win_bash_run": null}, "10": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "10", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "100": {"author": "UltiMaker", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"enable_extensive_warnings": false, "fPIC": true, "shared": false, "with_apps": false, "with_python_bindings": true}, "dependencies": {"101": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "nlohmann_json/3.11.2", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "102": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "ctre/3.7.2", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "103": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "pybind11/2.11.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "104": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "91": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "spdlog/1.15.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "92": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "fmt/11.1.3", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "93": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "range-v3/0.12.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Dulcificum changes the flavor, or dialect, of 3d printer commands", "generators": [], "generators_folder": null, "homepage": "https://ultimaker.com", "id": "100", "immutable_package_folder": null, "info": {"options": {"enable_extensive_warnings": "False", "shared": "False", "with_apps": "False", "with_python_bindings": "True"}, "python_requires": ["npmpackage/1.1.Z"], "requires": ["nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2:da39a3ee5e6b4b0d3255bfef95601890afd80709", "range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408:da39a3ee5e6b4b0d3255bfef95601890afd80709", "spdlog/1.15.Z", "fmt/11.1.Z", "ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80:da39a3ee5e6b4b0d3255bfef95601890afd80709", "cpython/3.12.Z", "pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b:da39a3ee5e6b4b0d3255bfef95601890afd80709"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "dulcificum/5.10.0", "languages": [], "license": "", "name": "dulcificum", "options": {"enable_extensive_warnings": "False", "shared": "False", "with_apps": "False", "with_python_bindings": "True"}, "options_definitions": {"enable_extensive_warnings": ["True", "False"], "shared": ["True", "False"], "with_apps": ["True", "False"], "with_python_bindings": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "34e85977e7b9d138ed409517e6238d09799fb8ff", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"npmpackage/1.1.0#4ee756f0e6532594bc38577aab07344a": {"path": "C:\\Users\\<USER>\\.conan2\\p\\npmpaba1a8a9744123\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\dulci493928b76138e\\e", "ref": "dulcificum/5.10.0#d7796f570134346c8cf8a45b2b677d63", "remote": null, "revision_mode": "hash", "rrev": "d7796f570134346c8cf8a45b2b677d63", "rrev_timestamp": **********.381, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cura", "curaengine", "gcode-generation", "3D-printing", "miraclegrue", "toolpath"], "upload_policy": null, "url": "https://github.com/Ultimaker/synsepalum-dulcificum", "user": null, "vendor": false, "version": "5.10.0", "win_bash": null, "win_bash_run": null}, "101": {"author": null, "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "JSON for Modern C++ parser and generator.", "generators": [], "generators_folder": null, "homepage": "https://github.com/nlohmann/json", "id": "101", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "nlohmann_json/3.11.2", "languages": [], "license": "MIT", "name": "nlohmann_json", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "2d1a5b1f5d673e1dab536bed20ce000b", "prev_timestamp": **********.574, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\nlohm720064fe68557\\e", "ref": "nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2", "remote": null, "revision_mode": "hash", "rrev": "1ded6ae5d200a68ac17c51d528b945e2", "rrev_timestamp": **********.653, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["json", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.11.2", "win_bash": null, "win_bash_run": null}, "102": {"author": null, "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Compile Time Regular Expression for C++17/20", "generators": [], "generators_folder": null, "homepage": "https://github.com/hanickadot/compile-time-regular-expressions", "id": "102", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "ctre/3.7.2", "languages": [], "license": ["Apache-2.0", "LLVM-exception"], "name": "ctre", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "a42728bed0e6742bba8e9f978ebaca8a", "prev_timestamp": **********.229, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\ctre2171e2d23ccf5\\e", "ref": "ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80", "remote": null, "revision_mode": "hash", "rrev": "86bd3592feebcdafd2ab7b8a1aad0c80", "rrev_timestamp": **********.536, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cpp17", "regex", "compile-time-regular-expressions", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.7.2", "win_bash": null, "win_bash_run": null}, "103": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Seamless operability between C++11 and Python", "generators": [], "generators_folder": null, "homepage": "https://github.com/pybind/pybind11", "id": "103", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "pybind11/2.11.1", "languages": [], "license": "BSD-3-Clause", "name": "pybind11", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "6e4692644a05d1d1622e4fec74091232", "prev_timestamp": **********.366, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\pybind3a1a109f9625\\e", "ref": "pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b", "remote": null, "revision_mode": "hash", "rrev": "9ac24fa2b6323656659eaf4e44fb7e0b", "rrev_timestamp": **********.328, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["pybind11", "python", "binding", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.11.1", "win_bash": null, "win_bash_run": null}, "104": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "104", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "105": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": true}, "dependencies": {"106": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "savitar/5.11.0-alpha.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "107": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "pugixml/1.14", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "109": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "110": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "sipbuildtool/0.3.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "pySavitar is a c++ implementation of 3mf loading with SIP python bindings", "generators": ["CMakeDeps"], "generators_folder": null, "homepage": null, "id": "105", "immutable_package_folder": null, "info": {"options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "python_requires": ["pyprojecttoolchain/0.2.Z", "sipbuildtool/0.3.Z"], "requires": ["savitar/5.11.Z", "pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91:f0ffe556848ee2b1d8dfe7d173a6d4df2258bebb", "cpython/3.12.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "pysavitar/5.11.0-alpha.0", "languages": [], "license": "LGPL-3.0", "name": "pysavitar", "options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "options_definitions": {"py_build_backend": ["ANY"], "py_build_requires": ["ANY"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "1a809b894882c71ef83a97772d3f6318ca9a64da", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"pyprojecttoolchain/0.2.0#d0d0d74876a9061a767bb2153c7952e3": {"path": "C:\\Users\\<USER>\\.conan2\\p\\pyproe6223f4e4f276\\e", "recipe": "Cache", "remote": null}, "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\pysavd2f605ac30bd8\\e", "ref": "pysavitar/5.11.0-alpha.0#683aa6a69435b316536c272b7807b883", "remote": null, "revision_mode": "hash", "rrev": "683aa6a69435b316536c272b7807b883", "rrev_timestamp": 1745929043.874, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "cura", "3mf", "c++"], "upload_policy": null, "url": "https://github.com/Ultimaker/pySavitar", "user": null, "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "106": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": ["Savitar"], "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": true}, "dependencies": {"107": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "pugixml/1.14", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "108": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "libSavitar is a c++ implementation of 3mf loading with SIP python bindings", "generators": ["VirtualRunEnv"], "generators_folder": null, "homepage": null, "id": "106", "immutable_package_folder": null, "info": {"options": {"shared": "True"}, "requires": ["pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91:f0ffe556848ee2b1d8dfe7d173a6d4df2258bebb"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "savitar/5.11.0-alpha.0", "languages": [], "license": "LGPL-3.0", "name": "savitar", "options": {"shared": "True"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "3156133023abac90ca0983888345ced2a172f4e1", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\savit952d062487d01\\e", "ref": "savitar/5.11.0-alpha.0#3a382967d301e3aae78b23a7f9080012", "remote": null, "revision_mode": "hash", "rrev": "3a382967d301e3aae78b23a7f9080012", "rrev_timestamp": **********.015, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "cura", "3mf", "c++"], "upload_policy": null, "url": "https://github.com/Ultimaker/libSavitar", "user": null, "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "107": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "header_only": false, "no_exceptions": false, "shared": false, "wchar_mode": false}, "dependencies": {}, "deprecated": null, "description": "Light-weight, simple and fast XML parser for C++ with XPath support", "generators": [], "generators_folder": null, "homepage": "https://pugixml.org/", "id": "107", "immutable_package_folder": null, "info": {"options": {"header_only": "False", "no_exceptions": "False", "shared": "False", "wchar_mode": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "pugixml/1.14", "languages": [], "license": "MIT", "name": "pugixml", "options": {"header_only": "False", "no_exceptions": "False", "shared": "False", "wchar_mode": "False"}, "options_definitions": {"header_only": ["True", "False"], "no_exceptions": ["True", "False"], "shared": ["True", "False"], "wchar_mode": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "f0ffe556848ee2b1d8dfe7d173a6d4df2258bebb", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\pugix1858ddf34b02d\\e", "ref": "pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91", "remote": null, "revision_mode": "hash", "rrev": "c6afdcf73d71858303d8260b0d76ff91", "rrev_timestamp": **********.231, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["xml-parser", "xpath", "xml", "dom"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.14", "win_bash": null, "win_bash_run": null}, "108": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "108", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "109": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "109", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "11": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "11", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "110": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "110", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "sipbuildtool/0.3.0", "languages": [], "license": null, "name": "sipbuildtool", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "3e8dce2119ddc1074e3863c15e8d63ff", "prev_timestamp": **********.282, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "ref": "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62", "remote": null, "revision_mode": "hash", "rrev": "262477bdb72ff6a09737695673de2b62", "rrev_timestamp": **********.164, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.3.0", "win_bash": null, "win_bash_run": null}, "111": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": true}, "dependencies": {"112": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "nest2d/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "113": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "nlopt/2.7.1", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "115": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "116": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "sipbuildtool/0.3.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "85": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "boost/1.86.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "88": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "clipper/6.4.2@ultimaker/stable", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Python bindings for libnest2d", "generators": [], "generators_folder": null, "homepage": null, "id": "111", "immutable_package_folder": null, "info": {"options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "python_requires": ["pyprojecttoolchain/0.2.Z", "sipbuildtool/0.3.Z"], "requires": ["nest2d/5.10.Z", "boost/1.86.0#1a9d6c7521c03c76356cfec29a82acb2:da39a3ee5e6b4b0d3255bfef95601890afd80709", "cpython/3.12.Z", "nlopt/2.7.Z", "clipper/6.4.Z@ultimaker/stable"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "pynest2d/5.10.0", "languages": [], "license": "LGPL-3.0", "name": "pynest2d", "options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "options_definitions": {"py_build_backend": ["ANY"], "py_build_requires": ["ANY"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "241d9608bf0d38c4e8c0a6b389653af4e32bc113", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"pyprojecttoolchain/0.2.0#d0d0d74876a9061a767bb2153c7952e3": {"path": "C:\\Users\\<USER>\\.conan2\\p\\pyproe6223f4e4f276\\e", "recipe": "Cache", "remote": null}, "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\pynes1f7693d3d6d61\\e", "ref": "pynest2d/5.10.0#ff5d39339d68c1cb296f83563e3eeacd", "remote": null, "revision_mode": "hash", "rrev": "ff5d39339d68c1cb296f83563e3eeacd", "rrev_timestamp": 1740566561.433, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "cura", "prusaslicer", "nesting", "c++", "bin packaging", "python", "sip"], "upload_policy": null, "url": "https://github.com/Ultimaker/pynest2d", "user": null, "vendor": false, "version": "5.10.0", "win_bash": null, "win_bash_run": null}, "112": {"author": null, "binary": "Build", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": "missing", "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": ["nest2d"], "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "geometries": "clipper", "header_only": false, "optimizer": "nlopt", "shared": true, "threading": "std"}, "dependencies": {"113": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "nlopt/2.7.1", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "114": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "85": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "boost/1.86.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "88": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "clipper/6.4.2@ultimaker/stable", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "2D irregular bin packaging and nesting library written in modern C++", "generators": [], "generators_folder": null, "homepage": null, "id": "112", "immutable_package_folder": null, "info": {"options": {"geometries": "clipper", "header_only": "False", "optimizer": "nlopt", "shared": "True", "threading": "std"}, "requires": ["clipper/6.4.Z@ultimaker/stable", "boost/1.86.0#1a9d6c7521c03c76356cfec29a82acb2:da39a3ee5e6b4b0d3255bfef95601890afd80709", "nlopt/2.7.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "nest2d/5.10.0", "languages": [], "license": null, "name": "nest2d", "options": {"geometries": "clipper", "header_only": "False", "optimizer": "nlopt", "shared": "True", "threading": "std"}, "options_definitions": {"geometries": ["clipper", "boost"], "header_only": ["True", "False"], "optimizer": ["nlopt", "optimlib"], "shared": ["True", "False"], "threading": ["std", "tbb", "omp", "none"]}, "options_description": null, "package_folder": null, "package_id": "43b4df8c9a534820adbabbd983f1f1b0b1708786", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\nest221682f7d4a644\\e", "ref": "nest2d/5.10.0#6f430e9fa21c308e0e6234649a6116ed", "remote": null, "revision_mode": "hash", "rrev": "6f430e9fa21c308e0e6234649a6116ed", "rrev_timestamp": **********.474, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "cura", "prusaslicer", "nesting", "c++", "bin packaging"], "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "5.10.0", "win_bash": null, "win_bash_run": null}, "113": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"enable_cxx_routines": true, "fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "Library for nonlinear optimization, wrapping many algorithms for global and local, constrained or unconstrained, optimization.", "generators": [], "generators_folder": null, "homepage": "https://github.com/stevengj/nlopt", "id": "113", "immutable_package_folder": null, "info": {"options": {"enable_cxx_routines": "True", "shared": "True"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "nlopt/2.7.1", "languages": [], "license": ["LGPL-2.1-or-later", "MIT"], "name": "nlopt", "options": {"enable_cxx_routines": "True", "shared": "True"}, "options_definitions": {"enable_cxx_routines": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "15314f2d6b8a2e9224dc02d3513c1c8cc17bac59", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\nlopt4b030b06b708b\\e", "ref": "nlopt/2.7.1#61296485856e44d13b39346ef54325f1", "remote": null, "revision_mode": "hash", "rrev": "61296485856e44d13b39346ef54325f1", "rrev_timestamp": **********.74, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["optimization", "nonlinear"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.7.1", "win_bash": null, "win_bash_run": null}, "114": {"author": null, "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "114", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "115": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "115", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "116": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "116", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "sipbuildtool/0.3.0", "languages": [], "license": null, "name": "sipbuildtool", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "3e8dce2119ddc1074e3863c15e8d63ff", "prev_timestamp": **********.282, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "ref": "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62", "remote": null, "revision_mode": "hash", "rrev": "262477bdb72ff6a09737695673de2b62", "rrev_timestamp": **********.164, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.3.0", "win_bash": null, "win_bash_run": null}, "12": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"13": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "15": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "17": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "12", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "13": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"14": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "13", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "14": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "14", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "15": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"16": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "15", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "16": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "16", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "17": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "17", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "18": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "18", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "19": {"author": "UltiMaker", "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": ["res/definitions", "res/extruders", "res/images", "res/intent", "res/meshes", "res/quality", "res/variants"], "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Cura Resources", "generators": [], "generators_folder": null, "homepage": null, "id": "19", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "cura_resources/5.11.0-alpha.0@ultimaker/testing", "languages": [], "license": "", "name": "cura_resources", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "shared-library", "prev": "2aacdedb072a42e0f3515e74c65a569d", "prev_timestamp": **********.386595, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\cura_8699fbeefcd93\\e", "ref": "cura_resources/5.11.0-alpha.0@ultimaker/testing#9f17199e1fff0085ec4f83d76f44fd2e", "remote": null, "revision_mode": "hash", "rrev": "9f17199e1fff0085ec4f83d76f44fd2e", "rrev_timestamp": **********.347, "settings": {}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "cura"], "upload_policy": null, "url": "https://github.com/Ultimaker/cura", "user": "ultimaker", "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "2": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {"3": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Convert text to and from Unicode", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/libiconv/", "id": "2", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "libiconv/1.17", "languages": [], "license": "LGPL-2.1-or-later", "name": "libiconv", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\libic8b953db1750fc\\e", "ref": "libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59", "remote": null, "revision_mode": "hash", "rrev": "1ae2f60ab5d08de1643a22a81b360c59", "rrev_timestamp": **********.465, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["iconv", "text", "encoding", "locale", "unicode", "conversion"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.17", "win_bash": true, "win_bash_run": null}, "20": {"author": "UltiMaker", "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["site-packages\\UM"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": ["resources", "plugins"], "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"i18n_extract": false}, "dependencies": {"21": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "gettext/0.22.5", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": false}, "39": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "pyarcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "40": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "arcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "41": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": "unrelated_mode", "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}}, "deprecated": null, "description": "A Python framework for building Desktop applications.", "generators": [], "generators_folder": null, "homepage": null, "id": "20", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "uranium/5.11.0-alpha.0@ultimaker/testing", "languages": [], "license": "LGPL-3.0", "name": "uranium", "options": {"i18n_extract": "False"}, "options_definitions": {"i18n_extract": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "6cd23a5aa3552001647b01f2cd804613", "prev_timestamp": **********.259665, "provides": null, "python_requires": {"translationextractor/2.3.0#d504876a4742c1b92bcd6e1d5ba7509a": {"path": "C:\\Users\\<USER>\\.conan2\\p\\transee40619501988\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\uranieadcc312c1a93\\e", "ref": "uranium/5.11.0-alpha.0@ultimaker/testing#241b252ef91c64676143ba23e1b7bd24", "remote": null, "revision_mode": "hash", "rrev": "241b252ef91c64676143ba23e1b7bd24", "rrev_timestamp": **********.349, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "python", "pyqt6", "qt", "3d-graphics", "3d-models", "python-framework"], "upload_policy": null, "url": "https://github.com/Ultimaker/uranium", "user": "ultimaker", "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "21": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"22": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "libiconv/1.17", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "24": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "25": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "automake/1.16.5", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "26": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "27": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "An internationalization and localization system for multilingual programs", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/gettext", "id": "21", "immutable_package_folder": null, "info": {"requires": ["libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59:ea5dc84deb25b32a77ec23c39a1811e3f441ee54"], "settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "gettext/0.22.5", "languages": [], "license": "GPL-3.0-or-later", "name": "gettext", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "f2629ff2b2a994555c09350ded2dc0c20d6e267a", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\gettee0511198a1073\\e", "ref": "gettext/0.22.5#a1f31cc77dee0345699745ef39686dd0", "remote": null, "revision_mode": "hash", "rrev": "a1f31cc77dee0345699745ef39686dd0", "rrev_timestamp": **********.89, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["intl", "libintl", "i18n"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "0.22.5", "win_bash": true, "win_bash_run": null}, "22": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {"23": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Convert text to and from Unicode", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/libiconv/", "id": "22", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "libiconv/1.17", "languages": [], "license": "LGPL-2.1-or-later", "name": "libiconv", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\libic8b953db1750fc\\e", "ref": "libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59", "remote": null, "revision_mode": "hash", "rrev": "1ae2f60ab5d08de1643a22a81b360c59", "rrev_timestamp": **********.465, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["iconv", "text", "encoding", "locale", "unicode", "conversion"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.17", "win_bash": true, "win_bash_run": null}, "23": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "23", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "24": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "24", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "25": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"26": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "27": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "32": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "33": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "38": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Automake is a tool for automatically generating Makefile.in files compliant with the GNU Coding Standards.", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/automake/", "id": "25", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "automake/1.16.5", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "automake", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "07094da42a0b39fd4b34760c5f1f3e7d", "prev_timestamp": **********.314, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autom654153fb7a0c4\\e", "ref": "automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50", "remote": null, "revision_mode": "hash", "rrev": "058bda3e21c36c9aa8425daf3c1faf50", "rrev_timestamp": **********.907, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["autotools", "configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.16.5", "win_bash": true, "win_bash_run": null}, "26": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"27": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "29": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "31": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "26", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "27": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"28": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "27", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "28": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "28", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "29": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"30": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "29", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "3": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "3", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "30": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "30", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "31": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "31", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "32": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"33": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "35": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "37": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "32", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "33": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"34": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "33", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "34": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "34", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "35": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"36": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "35", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "36": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "36", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "37": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "37", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "38": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "38", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "39": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": true}, "dependencies": {"40": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "arcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "41": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "full_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "46": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "cpython/3.12.2", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "70": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "71": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "sipbuildtool/0.3.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Communication library between internal components for Ultimaker software", "generators": ["CMakeDeps"], "generators_folder": null, "homepage": null, "id": "39", "immutable_package_folder": null, "info": {"options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "python_requires": ["pyprojecttoolchain/0.2.Z", "sipbuildtool/0.3.Z"], "requires": ["arcus/5.10.Z", "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:26799874b5d54539dcdcda58519609c8a3f677e0", "cpython/3.12.Z", "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:ea5dc84deb25b32a77ec23c39a1811e3f441ee54"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "pyarcus/5.10.0", "languages": [], "license": "LGPL-3.0", "name": "pyarcus", "options": {"py_build_backend": "sipbuild.api", "py_build_requires": "\"sip \u003e=6, \u003c7\", \"setuptools\u003e=40.8.0\", \"wheel\"", "shared": "True"}, "options_definitions": {"py_build_backend": ["ANY"], "py_build_requires": ["ANY"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "3e59cd37894a0686790cc1ed47a921cb4419712a", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"pyprojecttoolchain/0.2.0#d0d0d74876a9061a767bb2153c7952e3": {"path": "C:\\Users\\<USER>\\.conan2\\p\\pyproe6223f4e4f276\\e", "recipe": "Cache", "remote": null}, "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\pyarc93e83681583e3\\e", "ref": "pyarcus/5.10.0#45dd0835e9e65d080faa27e5669a957f", "remote": null, "revision_mode": "hash", "rrev": "45dd0835e9e65d080faa27e5669a957f", "rrev_timestamp": 1740990690.368, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "python", "binding", "sip", "cura", "protobuf"], "upload_policy": null, "url": "https://github.com/Ultimaker/pyArcus", "user": null, "vendor": false, "version": "5.10.0", "win_bash": null, "win_bash_run": null}, "4": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "4", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "40": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": ["Arcus"], "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": ["ws2_32"]}}, "default_options": {"fPIC": true, "shared": true}, "dependencies": {"41": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "full_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "43": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}, "44": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "protobuf/3.21.12", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Communication library between internal components for Ultimaker software", "generators": [], "generators_folder": null, "homepage": null, "id": "40", "immutable_package_folder": null, "info": {"options": {"enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "arcus", "sentry_send_binaries": "False", "shared": "True"}, "python_requires": ["sentrylibrary/1.0.Z"], "requires": ["protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:26799874b5d54539dcdcda58519609c8a3f677e0", "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:ea5dc84deb25b32a77ec23c39a1811e3f441ee54"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "arcus/5.10.0", "languages": [], "license": "LGPL-3.0", "name": "arcus", "options": {"enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "arcus", "sentry_send_binaries": "False", "shared": "True"}, "options_definitions": {"enable_sentry": ["True", "False"], "sentry_create_release": ["True", "False"], "sentry_project": ["ANY"], "sentry_send_binaries": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "420db1fd9bff770676c4a94c87bbde66303a3b21", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"sentrylibrary/1.0.0#004cb2aaa533fb28697dd9a302d652e8": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sentrb66cba1c889d9\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\arcus7c8966417fc6e\\e", "ref": "arcus/5.10.0#2eaae9c834c26c7010c5f8183c1f4b8b", "remote": null, "revision_mode": "hash", "rrev": "2eaae9c834c26c7010c5f8183c1f4b8b", "rrev_timestamp": 1741001398.787, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "binding", "cura", "protobuf", "c++"], "upload_policy": null, "url": "https://github.com/Ultimaker/libArcus", "user": null, "vendor": false, "version": "5.10.0", "win_bash": null, "win_bash_run": null}, "41": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"debug_suffix": true, "fPIC": true, "lite": false, "shared": false, "upb": false, "with_rtti": true, "with_zlib": true}, "dependencies": {"42": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Protocol Buffers - Google\u0027s data interchange format", "generators": [], "generators_folder": null, "homepage": "https://github.com/protocolbuffers/protobuf", "id": "41", "immutable_package_folder": null, "info": {"options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "requires": ["zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "protobuf/3.21.12", "languages": [], "license": "BSD-3-Clause", "name": "protobuf", "options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "options_definitions": {"debug_suffix": ["True", "False"], "lite": ["True", "False"], "shared": ["True", "False"], "with_rtti": ["True", "False"], "with_zlib": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "26799874b5d54539dcdcda58519609c8a3f677e0", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\proto7649b71794a3a\\e", "ref": "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1", "remote": null, "revision_mode": "hash", "rrev": "d927114e28de9f4691a6bbcdd9a529d1", "rrev_timestamp": **********.595, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["protocol-buffers", "protocol-compiler", "serialization", "rpc", "protocol-compiler"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.21.12", "win_bash": null, "win_bash_run": null}, "42": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "A Massively Spiffy Yet Delicately Unobtrusive Compression Library (Also Free, Not to Mention Unencumbered by Patents)", "generators": [], "generators_folder": null, "homepage": "https://zlib.net", "id": "42", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "zlib/1.3.1", "languages": [], "license": "Zlib", "name": "zlib", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\zlib204752602052d\\e", "ref": "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76", "remote": null, "revision_mode": "hash", "rrev": "b8bc2603263cf7eccbd6e17e66b0ed76", "rrev_timestamp": **********.16, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["zlib", "compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.3.1", "win_bash": null, "win_bash_run": null}, "43": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "43", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "44": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"debug_suffix": true, "fPIC": true, "lite": false, "shared": false, "upb": false, "with_rtti": true, "with_zlib": true}, "dependencies": {"45": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Protocol Buffers - Google\u0027s data interchange format", "generators": [], "generators_folder": null, "homepage": "https://github.com/protocolbuffers/protobuf", "id": "44", "immutable_package_folder": null, "info": {"options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "requires": ["zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "protobuf/3.21.12", "languages": [], "license": "BSD-3-Clause", "name": "protobuf", "options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "options_definitions": {"debug_suffix": ["True", "False"], "lite": ["True", "False"], "shared": ["True", "False"], "with_rtti": ["True", "False"], "with_zlib": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "26799874b5d54539dcdcda58519609c8a3f677e0", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\proto7649b71794a3a\\e", "ref": "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1", "remote": null, "revision_mode": "hash", "rrev": "d927114e28de9f4691a6bbcdd9a529d1", "rrev_timestamp": **********.595, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["protocol-buffers", "protocol-compiler", "serialization", "rpc", "protocol-compiler"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.21.12", "win_bash": null, "win_bash_run": null}, "45": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "A Massively Spiffy Yet Delicately Unobtrusive Compression Library (Also Free, Not to Mention Unencumbered by Patents)", "generators": [], "generators_folder": null, "homepage": "https://zlib.net", "id": "45", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "zlib/1.3.1", "languages": [], "license": "Zlib", "name": "zlib", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\zlib204752602052d\\e", "ref": "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76", "remote": null, "revision_mode": "hash", "rrev": "b8bc2603263cf7eccbd6e17e66b0ed76", "rrev_timestamp": **********.16, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["zlib", "compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.3.1", "win_bash": null, "win_bash_run": null}, "46": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"docstrings": true, "env_vars": true, "fPIC": true, "lto": false, "optimizations": false, "pymalloc": true, "shared": false, "with_bz2": true, "with_curses": true, "with_gdbm": true, "with_lzma": true, "with_nis": false, "with_sqlite3": true, "with_tkinter": true}, "dependencies": {"42": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "49": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "expat/2.7.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "50": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "libffi/3.4.4", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "66": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "mpdecimal/2.5.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "67": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "bzip2/1.0.8", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "68": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "sqlite3/3.45.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "69": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "xz_utils/5.4.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Python is a programming language that lets you work quickly and integrate systems more effectively.", "generators": [], "generators_folder": null, "homepage": "https://www.python.org", "id": "46", "immutable_package_folder": null, "info": {"options": {"optimizations": "False", "shared": "True", "with_bz2": "True", "with_lzma": "True", "with_sqlite3": "True", "with_tkinter": "False"}, "requires": ["openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b:b2a9d54575d4e12906b692dbf19ea41a55c6ad27", "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "expat/2.7.1#b0b67ba910c5147271b444139ca06953:63a0fcdae23b9c2be370c796d42667de454124ba", "libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f:ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "mpdecimal/2.5.1#cad87046d76460b8e2fc159194e50e7e:6299053df3519b3060382e0817677289b6e8dd71", "bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5:44c3558ce17b16c19d6f2822fae51dcc28bcbe0c", "sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137:2adac181d19fc98ec12fccffcd58445112eb1a65", "xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66:ea5dc84deb25b32a77ec23c39a1811e3f441ee54"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "cpython/3.12.2", "languages": [], "license": "Python-2.0", "name": "cpython", "options": {"env_vars": "True", "optimizations": "False", "shared": "True", "with_bz2": "True", "with_lzma": "True", "with_sqlite3": "True", "with_tkinter": "False"}, "options_definitions": {"env_vars": ["True", "False"], "optimizations": ["True", "False"], "shared": ["True", "False"], "with_bz2": ["True", "False"], "with_lzma": ["True", "False"], "with_sqlite3": ["True", "False"], "with_tkinter": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "841e85877bf61fb38776ae74dea1df77b45176a1", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\cpyth3f694d73f7f71\\e", "ref": "cpython/3.12.2#68cb44d6d7eeb24578b1c942ce16a8cd", "remote": null, "revision_mode": "hash", "rrev": "68cb44d6d7eeb24578b1c942ce16a8cd", "rrev_timestamp": **********.439, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["python", "cpython", "language", "script"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.12.2", "win_bash": null, "win_bash_run": null}, "47": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"386": false, "capieng_dialog": false, "enable_capieng": false, "enable_trace": false, "enable_weak_ssl_ciphers": false, "fPIC": true, "no_apps": false, "no_aria": false, "no_asm": false, "no_async": false, "no_autoload_config": false, "no_bf": false, "no_blake2": false, "no_camellia": false, "no_cast": false, "no_chacha": false, "no_cms": false, "no_comp": false, "no_ct": false, "no_deprecated": false, "no_des": false, "no_dgram": false, "no_dh": false, "no_dsa": false, "no_dso": false, "no_ec": false, "no_ecdh": false, "no_ecdsa": false, "no_engine": false, "no_filenames": false, "no_fips": false, "no_gost": false, "no_idea": false, "no_legacy": false, "no_md2": true, "no_md4": false, "no_mdc2": false, "no_module": false, "no_ocsp": false, "no_pinshared": false, "no_rc2": false, "no_rc4": false, "no_rc5": false, "no_rfc3779": false, "no_rmd160": false, "no_seed": false, "no_sm2": false, "no_sm3": false, "no_sm4": false, "no_sock": false, "no_srp": false, "no_srtp": false, "no_sse2": false, "no_ssl": false, "no_ssl3": false, "no_stdio": false, "no_threads": false, "no_tls1": false, "no_ts": false, "no_whirlpool": false, "no_zlib": false, "openssldir": null, "shared": false, "tls_security_level": null}, "dependencies": {"42": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "48": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "strawberryperl/********", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "A toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols", "generators": [], "generators_folder": null, "homepage": "https://github.com/openssl/openssl", "id": "47", "immutable_package_folder": null, "info": {"options": {"386": "False", "capieng_dialog": "False", "enable_capieng": "False", "enable_trace": "False", "enable_weak_ssl_ciphers": "False", "no_apps": "False", "no_aria": "False", "no_asm": "False", "no_async": "False", "no_autoload_config": "False", "no_bf": "False", "no_blake2": "False", "no_camellia": "False", "no_cast": "False", "no_chacha": "False", "no_cms": "False", "no_comp": "False", "no_ct": "False", "no_deprecated": "False", "no_des": "False", "no_dgram": "False", "no_dh": "False", "no_dsa": "False", "no_dso": "False", "no_ec": "False", "no_ecdh": "False", "no_ecdsa": "False", "no_engine": "False", "no_filenames": "False", "no_fips": "False", "no_gost": "False", "no_idea": "False", "no_legacy": "False", "no_md2": "True", "no_md4": "False", "no_mdc2": "False", "no_module": "False", "no_ocsp": "False", "no_pinshared": "False", "no_rc2": "False", "no_rc4": "False", "no_rc5": "False", "no_rfc3779": "False", "no_rmd160": "False", "no_seed": "False", "no_sm2": "False", "no_sm3": "False", "no_sm4": "False", "no_sock": "False", "no_srp": "False", "no_srtp": "False", "no_sse2": "False", "no_ssl": "False", "no_ssl3": "False", "no_stdio": "False", "no_threads": "False", "no_tls1": "False", "no_ts": "False", "no_whirlpool": "False", "no_zlib": "False", "openssldir": null, "shared": "False", "tls_security_level": null}, "requires": ["zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "openssl/3.5.0", "languages": [], "license": "Apache-2.0", "name": "openssl", "options": {"386": "False", "capieng_dialog": "False", "enable_capieng": "False", "enable_trace": "False", "enable_weak_ssl_ciphers": "False", "no_apps": "False", "no_aria": "False", "no_asm": "False", "no_async": "False", "no_autoload_config": "False", "no_bf": "False", "no_blake2": "False", "no_camellia": "False", "no_cast": "False", "no_chacha": "False", "no_cms": "False", "no_comp": "False", "no_ct": "False", "no_deprecated": "False", "no_des": "False", "no_dgram": "False", "no_dh": "False", "no_dsa": "False", "no_dso": "False", "no_ec": "False", "no_ecdh": "False", "no_ecdsa": "False", "no_engine": "False", "no_filenames": "False", "no_fips": "False", "no_gost": "False", "no_idea": "False", "no_legacy": "False", "no_md2": "True", "no_md4": "False", "no_mdc2": "False", "no_module": "False", "no_ocsp": "False", "no_pinshared": "False", "no_rc2": "False", "no_rc4": "False", "no_rc5": "False", "no_rfc3779": "False", "no_rmd160": "False", "no_seed": "False", "no_sm2": "False", "no_sm3": "False", "no_sm4": "False", "no_sock": "False", "no_srp": "False", "no_srtp": "False", "no_sse2": "False", "no_ssl": "False", "no_ssl3": "False", "no_stdio": "False", "no_threads": "False", "no_tls1": "False", "no_ts": "False", "no_whirlpool": "False", "no_zlib": "False", "openssldir": null, "shared": "False", "tls_security_level": null}, "options_definitions": {"386": ["True", "False"], "capieng_dialog": ["True", "False"], "enable_capieng": ["True", "False"], "enable_trace": ["True", "False"], "enable_weak_ssl_ciphers": ["True", "False"], "no_apps": ["True", "False"], "no_aria": ["True", "False"], "no_asm": ["True", "False"], "no_async": ["True", "False"], "no_autoload_config": ["True", "False"], "no_bf": ["True", "False"], "no_blake2": ["True", "False"], "no_camellia": ["True", "False"], "no_cast": ["True", "False"], "no_chacha": ["True", "False"], "no_cms": ["True", "False"], "no_comp": ["True", "False"], "no_ct": ["True", "False"], "no_deprecated": ["True", "False"], "no_des": ["True", "False"], "no_dgram": ["True", "False"], "no_dh": ["True", "False"], "no_dsa": ["True", "False"], "no_dso": ["True", "False"], "no_ec": ["True", "False"], "no_ecdh": ["True", "False"], "no_ecdsa": ["True", "False"], "no_engine": ["True", "False"], "no_filenames": ["True", "False"], "no_fips": ["True", "False"], "no_gost": ["True", "False"], "no_idea": ["True", "False"], "no_legacy": ["True", "False"], "no_md2": ["True", "False"], "no_md4": ["True", "False"], "no_mdc2": ["True", "False"], "no_module": ["True", "False"], "no_ocsp": ["True", "False"], "no_pinshared": ["True", "False"], "no_rc2": ["True", "False"], "no_rc4": ["True", "False"], "no_rc5": ["True", "False"], "no_rfc3779": ["True", "False"], "no_rmd160": ["True", "False"], "no_seed": ["True", "False"], "no_sm2": ["True", "False"], "no_sm3": ["True", "False"], "no_sm4": ["True", "False"], "no_sock": ["True", "False"], "no_srp": ["True", "False"], "no_srtp": ["True", "False"], "no_sse2": ["True", "False"], "no_ssl": ["True", "False"], "no_ssl3": ["True", "False"], "no_stdio": ["True", "False"], "no_threads": ["True", "False"], "no_tls1": ["True", "False"], "no_ts": ["True", "False"], "no_whirlpool": ["True", "False"], "no_zlib": ["True", "False"], "openssldir": [null, "ANY"], "shared": ["True", "False"], "tls_security_level": [null, "0", "1", "2", "3", "4", "5"]}, "options_description": null, "package_folder": null, "package_id": "b2a9d54575d4e12906b692dbf19ea41a55c6ad27", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\opensdec471bc6e51a\\e", "ref": "openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b", "remote": null, "revision_mode": "hash", "rrev": "62d21a127982d5ddc42a83cffc147a4b", "rrev_timestamp": 1749434712.781, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["ssl", "tls", "encryption", "security"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.5.0", "win_bash": null, "win_bash_run": null}, "48": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Strawberry Perl for Windows.", "generators": [], "generators_folder": null, "homepage": "http://strawberryperl.com", "id": "48", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "strawberryperl/******** is only available for x86 and x86_64 architectures.", "invalid_build": false, "label": "strawberryperl/********", "languages": [], "license": ["Artistic-1.0", "GPL-1.0"], "name": "strawberryperl", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "a0cd51c51fe9010370187244af885b0efcc5b69b", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\straw0ce3853788a29\\e", "ref": "strawberryperl/********#707032463aa0620fa17ec0d887f5fe41", "remote": null, "revision_mode": "hash", "rrev": "707032463aa0620fa17ec0d887f5fe41", "rrev_timestamp": **********.643, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["perl", "interpreter", "windows"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "********", "win_bash": null, "win_bash_run": null}, "49": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"char_type": "char", "fPIC": true, "large_size": false, "shared": false}, "dependencies": {}, "deprecated": null, "description": "Fast streaming XML parser written in C.", "generators": [], "generators_folder": null, "homepage": "https://github.com/libexpat/libexpat", "id": "49", "immutable_package_folder": null, "info": {"options": {"char_type": "char", "large_size": "False", "shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "expat/2.7.1", "languages": [], "license": "MIT", "name": "expat", "options": {"char_type": "char", "large_size": "False", "shared": "False"}, "options_definitions": {"char_type": ["char", "wchar_t", "ushort"], "large_size": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "63a0fcdae23b9c2be370c796d42667de454124ba", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\expat2b147d89193e8\\e", "ref": "expat/2.7.1#b0b67ba910c5147271b444139ca06953", "remote": null, "revision_mode": "hash", "rrev": "b0b67ba910c5147271b444139ca06953", "rrev_timestamp": **********.935, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["xml", "parsing"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.7.1", "win_bash": null, "win_bash_run": null}, "5": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"12": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "13": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "18": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "6": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "7": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Automake is a tool for automatically generating Makefile.in files compliant with the GNU Coding Standards.", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/automake/", "id": "5", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "automake/1.16.5", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "automake", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "07094da42a0b39fd4b34760c5f1f3e7d", "prev_timestamp": **********.314, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autom654153fb7a0c4\\e", "ref": "automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50", "remote": null, "revision_mode": "hash", "rrev": "058bda3e21c36c9aa8425daf3c1faf50", "rrev_timestamp": **********.907, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["autotools", "configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.16.5", "win_bash": true, "win_bash_run": null}, "50": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {"51": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "52": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "automake/1.16.5", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "53": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "54": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "A portable, high level programming interface to various calling conventions", "generators": [], "generators_folder": null, "homepage": "https://sourceware.org/libffi/", "id": "50", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "libffi/3.4.4", "languages": [], "license": "MIT", "name": "libffi", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\libffcdddad8c31bca\\e", "ref": "libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f", "remote": null, "revision_mode": "hash", "rrev": "a1442e924f14664d9545dfcfe66d751f", "rrev_timestamp": **********.64, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["runtime", "foreign-function-interface", "runtime-library"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.4.4", "win_bash": true, "win_bash_run": null}, "51": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "51", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "52": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"53": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "54": {"build": false, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "59": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "autoconf/2.71", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "60": {"build": true, "direct": false, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "65": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Automake is a tool for automatically generating Makefile.in files compliant with the GNU Coding Standards.", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/automake/", "id": "52", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "automake/1.16.5", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "automake", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "07094da42a0b39fd4b34760c5f1f3e7d", "prev_timestamp": **********.314, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autom654153fb7a0c4\\e", "ref": "automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50", "remote": null, "revision_mode": "hash", "rrev": "058bda3e21c36c9aa8425daf3c1faf50", "rrev_timestamp": **********.907, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["autotools", "configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.16.5", "win_bash": true, "win_bash_run": null}, "53": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"54": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "56": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "58": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "53", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "54": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"55": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "54", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "55": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "55", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "56": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"57": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "56", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "57": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "57", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "58": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "58", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "59": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"60": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "62": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "64": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "59", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "6": {"author": null, "binary": "Skip", "binary_remote": "conancenter", "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"11": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "7": {"build": false, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "9": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "m4/1.4.19", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Autoconf is an extensible package of M4 macros that produce shell scripts to automatically configure software source code packages", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/autoconf/", "id": "6", "immutable_package_folder": null, "info": {"settings": {"os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "autoconf/2.71", "languages": [], "license": ["GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "autoconf", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "ebec3dc6d7f6b907b3ada0c3d3cdc83613a2b715", "package_type": "application", "prev": "b3a5091ff4602fbd9feebc093052d712", "prev_timestamp": **********.635, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\autoc8866aef2bd628\\e", "ref": "autoconf/2.71#51077f068e61700d65bb05541ea1e4b0", "remote": null, "revision_mode": "hash", "rrev": "51077f068e61700d65bb05541ea1e4b0", "rrev_timestamp": **********.903, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["configure", "build"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.71", "win_bash": true, "win_bash_run": null}, "60": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"61": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "60", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "61": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "61", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "62": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"63": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "62", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "63": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "63", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "64": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "64", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "65": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "65", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "66": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"cxx": true, "fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "mpdecimal is a package for correctly-rounded arbitrary precision decimal floating point arithmetic.", "generators": [], "generators_folder": null, "homepage": "http://www.bytereef.org/mpdecimal", "id": "66", "immutable_package_folder": null, "info": {"options": {"cxx": "True", "shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": "mpdecimal/2.5.1 currently does not supported armv8. Contributions are welcomed", "invalid_build": false, "label": "mpdecimal/2.5.1", "languages": [], "license": "BSD-2-Clause", "name": "mpdecimal", "options": {"cxx": "True", "shared": "False"}, "options_definitions": {"cxx": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "6299053df3519b3060382e0817677289b6e8dd71", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\mpdeccd2ace5dc8a27\\e", "ref": "mpdecimal/2.5.1#cad87046d76460b8e2fc159194e50e7e", "remote": null, "revision_mode": "hash", "rrev": "cad87046d76460b8e2fc159194e50e7e", "rrev_timestamp": **********.083, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["multiprecision", "library"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.5.1", "win_bash": null, "win_bash_run": null}, "67": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"build_executable": true, "fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "bzip2 is a free and open-source file compression program that uses the Burrows Wheeler algorithm.", "generators": [], "generators_folder": null, "homepage": "https://sourceware.org/bzip2", "id": "67", "immutable_package_folder": null, "info": {"options": {"build_executable": "True", "shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "bzip2/1.0.8", "languages": [], "license": "bzip2-1.0.8", "name": "bzip2", "options": {"build_executable": "True", "shared": "False"}, "options_definitions": {"build_executable": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "44c3558ce17b16c19d6f2822fae51dcc28bcbe0c", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\bzip28d32b93e26a49\\e", "ref": "bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5", "remote": null, "revision_mode": "hash", "rrev": "00b4a4658791c1f06914e087f0e792f5", "rrev_timestamp": **********.793, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["data-compressor", "file-compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.0.8", "win_bash": null, "win_bash_run": null}, "68": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"build_executable": true, "disable_gethostuuid": false, "enable_column_metadata": true, "enable_dbpage_vtab": false, "enable_dbstat_vtab": false, "enable_default_secure_delete": false, "enable_default_vfs": true, "enable_explain_comments": false, "enable_fts3": false, "enable_fts3_parenthesis": false, "enable_fts4": false, "enable_fts5": false, "enable_json1": false, "enable_math_functions": true, "enable_preupdate_hook": false, "enable_rtree": true, "enable_soundex": false, "enable_unlock_notify": true, "fPIC": true, "max_blob_size": null, "max_column": null, "max_variable_number": null, "omit_deprecated": false, "omit_load_extension": false, "shared": false, "threadsafe": 1, "use_alloca": false, "use_uri": false}, "dependencies": {}, "deprecated": null, "description": "Self-contained, serverless, in-process SQL database engine.", "generators": [], "generators_folder": null, "homepage": "https://www.sqlite.org", "id": "68", "immutable_package_folder": null, "info": {"options": {"build_executable": "True", "disable_gethostuuid": "False", "enable_column_metadata": "True", "enable_dbpage_vtab": "False", "enable_dbstat_vtab": "False", "enable_default_secure_delete": "False", "enable_default_vfs": "True", "enable_explain_comments": "False", "enable_fts3": "False", "enable_fts3_parenthesis": "False", "enable_fts4": "False", "enable_fts5": "False", "enable_json1": "False", "enable_math_functions": "True", "enable_preupdate_hook": "False", "enable_rtree": "True", "enable_soundex": "False", "enable_unlock_notify": "True", "max_blob_size": null, "max_column": null, "max_variable_number": null, "omit_deprecated": "False", "omit_load_extension": "False", "shared": "False", "threadsafe": "1", "use_alloca": "False", "use_uri": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "sqlite3/3.45.2", "languages": [], "license": "Unlicense", "name": "sqlite3", "options": {"build_executable": "True", "disable_gethostuuid": "False", "enable_column_metadata": "True", "enable_dbpage_vtab": "False", "enable_dbstat_vtab": "False", "enable_default_secure_delete": "False", "enable_default_vfs": "True", "enable_explain_comments": "False", "enable_fts3": "False", "enable_fts3_parenthesis": "False", "enable_fts4": "False", "enable_fts5": "False", "enable_json1": "False", "enable_math_functions": "True", "enable_preupdate_hook": "False", "enable_rtree": "True", "enable_soundex": "False", "enable_unlock_notify": "True", "max_blob_size": null, "max_column": null, "max_variable_number": null, "omit_deprecated": "False", "omit_load_extension": "False", "shared": "False", "threadsafe": "1", "use_alloca": "False", "use_uri": "False"}, "options_definitions": {"build_executable": ["True", "False"], "disable_gethostuuid": ["True", "False"], "enable_column_metadata": ["True", "False"], "enable_dbpage_vtab": ["True", "False"], "enable_dbstat_vtab": ["True", "False"], "enable_default_secure_delete": ["True", "False"], "enable_default_vfs": ["True", "False"], "enable_explain_comments": ["True", "False"], "enable_fts3": ["True", "False"], "enable_fts3_parenthesis": ["True", "False"], "enable_fts4": ["True", "False"], "enable_fts5": ["True", "False"], "enable_json1": ["True", "False"], "enable_math_functions": ["True", "False"], "enable_preupdate_hook": ["True", "False"], "enable_rtree": ["True", "False"], "enable_soundex": ["True", "False"], "enable_unlock_notify": ["True", "False"], "max_blob_size": [null, "ANY"], "max_column": [null, "ANY"], "max_variable_number": [null, "ANY"], "omit_deprecated": ["True", "False"], "omit_load_extension": ["True", "False"], "shared": ["True", "False"], "threadsafe": ["0", "1", "2"], "use_alloca": ["True", "False"], "use_uri": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "2adac181d19fc98ec12fccffcd58445112eb1a65", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\sqlit63bae08d0adf4\\e", "ref": "sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137", "remote": null, "revision_mode": "hash", "rrev": "60f2d3278e7bc12c8ef02ac75119c137", "rrev_timestamp": 1727344283.953, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["sqlite", "database", "sql", "serverless"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.45.2", "win_bash": null, "win_bash_run": null}, "69": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "XZ Utils is free general-purpose data compression software with a high compression ratio. XZ Utils were written for POSIX-like systems, but also work on some not-so-POSIX systems. XZ Utils are the successor to LZMA Utils.", "generators": [], "generators_folder": null, "homepage": "https://tukaani.org/xz", "id": "69", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "xz_utils/5.4.5", "languages": [], "license": ["Unlicense", "LGPL-2.1-or-later", "GPL-2.0-or-later", "GPL-3.0-or-later"], "name": "xz_utils", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\xz_utc36de450e687d\\e", "ref": "xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66", "remote": null, "revision_mode": "hash", "rrev": "b885d1d79c9d30cff3803f7f551dbe66", "rrev_timestamp": **********.984, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["lzma", "xz", "compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "5.4.5", "win_bash": null, "win_bash_run": null}, "7": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"8": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "7", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "70": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "70", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "71": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "71", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "sipbuildtool/0.3.0", "languages": [], "license": null, "name": "sipbuildtool", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "3e8dce2119ddc1074e3863c15e8d63ff", "prev_timestamp": **********.282, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\sipbufcc77be6848da\\e", "ref": "sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62", "remote": null, "revision_mode": "hash", "rrev": "262477bdb72ff6a09737695673de2b62", "rrev_timestamp": **********.164, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.3.0", "win_bash": null, "win_bash_run": null}, "72": {"author": "UltiMaker", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": ["_CuraEngine"], "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"enable_arcus": true, "enable_benchmarks": false, "enable_extensive_warnings": false, "enable_plugins": true, "enable_remote_plugins": false, "with_cura_resources": false}, "dependencies": {"40": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "arcus/5.10.0", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "41": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "42": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "full_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "73": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "scripta/1.1.0-alpha.0+b96045@ultimaker/testing", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "75": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "neargye-semver/0.3.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "76": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "curaengine_grpc_definitions/0.3.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "77": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "asio-grpc/2.9.2", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "78": {"build": false, "direct": false, "force": true, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "grpc/1.54.3", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "79": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "abseil/20230802.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "80": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "full_mode", "ref": "c-ares/1.34.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "81": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "full_mode", "ref": "re2/20230301", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "85": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "boost/1.86.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "88": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "clipper/6.4.2@ultimaker/stable", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "89": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "minor_mode", "ref": "rapidjson/cci.20230929", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "90": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "stb/cci.20230920", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "91": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "spdlog/1.15.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "92": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "full_mode", "ref": "fmt/11.1.3", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "93": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "range-v3/0.12.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "94": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "mapbox-wagyu/0.5.0@ultimaker/stable", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "95": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "mapbox-geometry/2.0.3", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "96": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "mapbox-variant/1.2.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "97": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0", "run": true, "skip": false, "test": true, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Powerful, fast and robust engine for converting 3D models into g-code instructions for 3D printers. It is part of the larger open source project Cura.", "generators": [], "generators_folder": null, "homepage": null, "id": "72", "immutable_package_folder": null, "info": {"options": {"enable_arcus": "True", "enable_benchmarks": "False", "enable_extensive_warnings": "False", "enable_plugins": "True", "enable_remote_plugins": "False", "enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "curaengine", "sentry_send_binaries": "False", "with_cura_resources": "False"}, "python_requires": ["npmpackage/1.1.Z", "sentrylibrary/1.0.Z"], "requires": ["scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785:da39a3ee5e6b4b0d3255bfef95601890afd80709", "arcus/5.10.Z", "neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd:da39a3ee5e6b4b0d3255bfef95601890afd80709", "curaengine_grpc_definitions/0.3.2#c1ff2681188db77f8da6a244ce207ca7:52a249a0751ebe01c61a22236b79d97677713bc2", "asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039:961db76d88f17792fac56ff31488b67885f21476", "grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2:add1e95e5a5e87ffbd4afae6119271ef187171e1", "abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b:d7a0b98367877dc897e1afd915bef602a8b0c3e6", "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:26799874b5d54539dcdcda58519609c8a3f677e0", "c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6:198f06764a7c73c097875ad130b7ca9b86884e02", "openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b:b2a9d54575d4e12906b692dbf19ea41a55c6ad27", "re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4:5c1ca629793fc7f14b78bc19617db00a4d6b3d2f", "clipper/6.4.Z@ultimaker/stable", "boost/1.86.0#1a9d6c7521c03c76356cfec29a82acb2:da39a3ee5e6b4b0d3255bfef95601890afd80709", "rapidjson/cci", "stb/cci.20230920#ed79bd361e974a99137f214efb117eef:24b381c7532f70c284a2a67cc83f779b3c0042fb", "spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7:87c963d0e391a1419624873329f5ab4cc8db0cf8", "fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae:3d87232490906d668db42ba1c776b65f7f328833", "range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408:da39a3ee5e6b4b0d3255bfef95601890afd80709", "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "mapbox-wagyu/0.5.0@ultimaker/stable#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709", "mapbox-geometry/2.0.3#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709", "mapbox-variant/1.2.0#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "curaengine/5.11.0-alpha.0@ultimaker/testing", "languages": [], "license": "AGPL-3.0", "name": "curaengine", "options": {"enable_arcus": "True", "enable_benchmarks": "False", "enable_extensive_warnings": "False", "enable_plugins": "True", "enable_remote_plugins": "False", "enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "curaengine", "sentry_send_binaries": "False", "with_cura_resources": "False"}, "options_definitions": {"enable_arcus": ["True", "False"], "enable_benchmarks": ["True", "False"], "enable_extensive_warnings": ["True", "False"], "enable_plugins": ["True", "False"], "enable_remote_plugins": ["True", "False"], "enable_sentry": ["True", "False"], "sentry_create_release": ["True", "False"], "sentry_project": ["ANY"], "sentry_send_binaries": ["True", "False"], "with_cura_resources": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "7345f919c97f297237d8bbf185538798c59378b3", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"npmpackage/1.1.0#4ee756f0e6532594bc38577aab07344a": {"path": "C:\\Users\\<USER>\\.conan2\\p\\npmpaba1a8a9744123\\e", "recipe": "Cache", "remote": null}, "sentrylibrary/1.0.0#004cb2aaa533fb28697dd9a302d652e8": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sentrb66cba1c889d9\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\curae188c76feed75c\\e", "ref": "curaengine/5.11.0-alpha.0@ultimaker/testing#9870b0f94712770a54adbd8a0278ecff", "remote": null, "revision_mode": "hash", "rrev": "9870b0f94712770a54adbd8a0278ecff", "rrev_timestamp": 1750429565.13, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cura", "protobuf", "gcode", "c++", "curaengine", "libarcus", "gcode-generation", "3D-printing"], "upload_policy": null, "url": "https://github.com/Ultimaker/CuraEngine", "user": "ultimaker", "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "73": {"author": "UltiMaker", "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"enable_extensive_warnings": false, "enable_testing": false}, "dependencies": {"74": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": null, "ref": "standardprojectsettings/0.2.0@ultimaker/stable", "run": true, "skip": false, "test": true, "transitive_headers": true, "transitive_libs": true, "visible": false}}, "deprecated": null, "description": "A visual debugger for CuraEngine called after the moth species Habrosyne scripta", "generators": [], "generators_folder": null, "homepage": null, "id": "73", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "scripta/1.1.0-alpha.0+b96045@ultimaker/testing", "languages": [], "license": "", "name": "scripta", "options": {"enable_extensive_warnings": "False", "enable_testing": "False"}, "options_definitions": {"enable_extensive_warnings": ["True", "False"], "enable_testing": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "d2d84c4ec705cbfa88d3313ea69b4b2e", "prev_timestamp": **********.699, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\scrip6defc43b305ad\\e", "ref": "scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785", "remote": null, "revision_mode": "hash", "rrev": "3786da67f9aa951719291aaab18d0785", "rrev_timestamp": **********.932, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cura", "c++", "curaengine", "vtu", "gcode-generation", "3d-printing"], "upload_policy": null, "url": "https://github.com/Ultimaker/Scripta_public", "user": "ultimaker", "vendor": false, "version": "1.1.0-alpha.0+b96045", "win_bash": null, "win_bash_run": null}, "74": {"author": null, "binary": "Skip", "binary_remote": "cura-conan2", "build_folder": null, "build_id": null, "build_policy": null, "channel": "stable", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "74", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0@ultimaker/stable", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.563, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb20ecc831767f\\e", "ref": "standardprojectsettings/0.2.0@ultimaker/stable#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.92, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": "ultimaker", "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "75": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Semantic Versioning for modern C++", "generators": [], "generators_folder": null, "homepage": "https://github.com/Neargye/semver", "id": "75", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "neargye-semver/0.3.0", "languages": [], "license": "MIT", "name": "neargye-semver", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "09b44697fa4ef5f27606e5eb1da16bcf", "prev_timestamp": **********.054, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\nearg370e03c3ea965\\e", "ref": "neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd", "remote": null, "revision_mode": "hash", "rrev": "799b2e371d5be985a073214e029ea6cd", "rrev_timestamp": **********.331, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["semver", "semantic", "versioning", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "0.3.0", "win_bash": null, "win_bash_run": null}, "76": {"author": "UltiMaker", "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {"41": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "minor_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "77": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "asio-grpc/2.9.2", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "78": {"build": false, "direct": false, "force": true, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "grpc/1.54.3", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "79": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "abseil/20230802.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "80": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "minor_mode", "ref": "c-ares/1.34.5", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "81": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "minor_mode", "ref": "re2/20230301", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "85": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "full_mode", "ref": "boost/1.86.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "86": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "protobuf/3.21.12", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "The gRPC definitions for CuraEngine plugins.", "generators": [], "generators_folder": null, "homepage": null, "id": "76", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "requires": ["asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039:961db76d88f17792fac56ff31488b67885f21476", "grpc/1.54.Z", "abseil/20230802.1.Z", "protobuf/3.21.Z", "c-ares/1.34.Z", "openssl/3.5.Z", "re2/20230301.0.Z", "zlib/1.3.Z", "boost/1.86.0#1a9d6c7521c03c76356cfec29a82acb2:da39a3ee5e6b4b0d3255bfef95601890afd80709"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "curaengine_grpc_definitions/0.3.2", "languages": [], "license": "MIT", "name": "curaengine_grpc_definitions", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "52a249a0751ebe01c61a22236b79d97677713bc2", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\curaeac0166be19773\\e", "ref": "curaengine_grpc_definitions/0.3.2#c1ff2681188db77f8da6a244ce207ca7", "remote": null, "revision_mode": "hash", "rrev": "c1ff2681188db77f8da6a244ce207ca7", "rrev_timestamp": **********.255, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "20", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cura", "protobuf", "gcode", "grpc", "curaengine", "plugin", "3D-printing"], "upload_policy": null, "url": "https://github.com/Ultimaker/curaengine_grpc_definitions", "user": null, "vendor": false, "version": "0.3.2", "win_bash": null, "win_bash_run": null}, "77": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"backend": "boost"}, "dependencies": {"41": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "42": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "47": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "unrelated_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "78": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "grpc/1.54.3", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "79": {"build": false, "direct": false, "force": false, "headers": true, "libs": true, "package_id_mode": "unrelated_mode", "ref": "abseil/20230802.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "80": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "unrelated_mode", "ref": "c-ares/1.34.5", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "81": {"build": false, "direct": false, "force": false, "headers": false, "libs": true, "package_id_mode": "unrelated_mode", "ref": "re2/20230301", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "85": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "unrelated_mode", "ref": "boost/1.86.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}}, "deprecated": null, "description": "Asynchronous gRPC with Asio/unified executors", "generators": [], "generators_folder": null, "homepage": "https://github.com/Tradias/asio-grpc", "id": "77", "immutable_package_folder": null, "info": {"options": {"local_allocator": "recycling_allocator"}}, "info_invalid": null, "invalid_build": false, "label": "asio-grpc/2.9.2", "languages": [], "license": "Apache-2.0", "name": "asio-grpc", "options": {"backend": "boost", "local_allocator": "recycling_allocator"}, "options_definitions": {"backend": ["boost", "asio", "unifex"], "local_allocator": ["memory_resource", "boost_container", "recycling_allocator"]}, "options_description": null, "package_folder": null, "package_id": "961db76d88f17792fac56ff31488b67885f21476", "package_type": "header-library", "prev": "7d9f114a4b45b107848f90a976f5c538", "prev_timestamp": **********.86, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\asio-6c1e25588b7f6\\e", "ref": "asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039", "remote": null, "revision_mode": "hash", "rrev": "a44093413738811a2159b761c1bf1039", "rrev_timestamp": **********.181, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["cpp", "asynchronous", "grpc", "asio", "asynchronous-programming", "cpp17", "coroutine", "cpp20", "executors", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.9.2", "win_bash": null, "win_bash_run": null}, "78": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"codegen": true, "cpp_plugin": true, "csharp_ext": false, "csharp_plugin": true, "fPIC": true, "node_plugin": true, "objective_c_plugin": true, "otel_plugin": false, "php_plugin": true, "python_plugin": true, "ruby_plugin": true, "secure": false, "shared": false, "with_libsystemd": true}, "dependencies": {"41": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "protobuf/3.21.12", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "42": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": null, "visible": true}, "47": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "openssl/3.5.0", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "79": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "abseil/20230802.1", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "80": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "c-ares/1.34.5", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "81": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "re2/20230301", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}, "82": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "cmake/3.31.8", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}, "83": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "protobuf/3.21.12", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "Google\u0027s RPC (remote procedure call) library and framework.", "generators": [], "generators_folder": null, "homepage": "https://github.com/grpc/grpc", "id": "78", "immutable_package_folder": null, "info": {"options": {"codegen": "True", "cpp_plugin": "True", "csharp_ext": "False", "csharp_plugin": "False", "node_plugin": "False", "objective_c_plugin": "False", "php_plugin": "False", "python_plugin": "False", "ruby_plugin": "False", "shared": "False"}, "requires": ["abseil/20230802.1.Z", "protobuf/3.21.Z", "c-ares/1.34.Z", "openssl/3.5.Z", "re2/20230301.0.Z", "zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "grpc/1.54.3", "languages": [], "license": "Apache-2.0", "name": "grpc", "options": {"codegen": "True", "cpp_plugin": "True", "csharp_ext": "False", "csharp_plugin": "False", "node_plugin": "False", "objective_c_plugin": "False", "php_plugin": "False", "python_plugin": "False", "ruby_plugin": "False", "secure": "False", "shared": "False"}, "options_definitions": {"codegen": ["True", "False"], "cpp_plugin": ["True", "False"], "csharp_ext": ["True", "False"], "csharp_plugin": ["True", "False"], "node_plugin": ["True", "False"], "objective_c_plugin": ["True", "False"], "php_plugin": ["True", "False"], "python_plugin": ["True", "False"], "ruby_plugin": ["True", "False"], "secure": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "add1e95e5a5e87ffbd4afae6119271ef187171e1", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\grpcb0fc801cbbe1b\\e", "ref": "grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2", "remote": null, "revision_mode": "hash", "rrev": "8d094817df26303aa6cb74eaaea622d2", "rrev_timestamp": 1748224831.445, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["rpc"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.54.3", "win_bash": null, "win_bash_run": null}, "79": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "Abseil Common Libraries (C++) from Google", "generators": [], "generators_folder": null, "homepage": "https://github.com/abseil/abseil-cpp", "id": "79", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "abseil/20230802.1", "languages": [], "license": "Apache-2.0", "name": "abseil", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "d7a0b98367877dc897e1afd915bef602a8b0c3e6", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\absei1c6a6c8e929ed\\e", "ref": "abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b", "remote": null, "revision_mode": "hash", "rrev": "f0f91485b111dc9837a68972cb19ca7b", "rrev_timestamp": **********.497, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["algorithm", "container", "google", "common", "utility"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "20230802.1", "win_bash": null, "win_bash_run": null}, "8": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": false, "packages": "base-devel,binutils,gcc"}, "dependencies": {}, "deprecated": null, "description": "MSYS2 is a software distro and building platform for Windows", "generators": [], "generators_folder": null, "homepage": "http://www.msys2.org", "id": "8", "immutable_package_folder": null, "info": {"options": {"additional_packages": null, "exclude_files": "*/link.exe", "packages": "base-devel,binutils,gcc"}, "settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": "Only Windows x64 supported", "invalid_build": false, "label": "msys2/cci.latest", "languages": [], "license": "MSYS license", "name": "msys2", "options": {"additional_packages": null, "exclude_files": "*/link.exe", "no_kill": "False", "packages": "base-devel,binutils,gcc"}, "options_definitions": {"additional_packages": [null, "ANY"], "exclude_files": ["ANY"], "no_kill": ["True", "False"], "packages": ["ANY"]}, "options_description": null, "package_folder": null, "package_id": "e9a3a73102f4f40e2501af7e7bd52935a57c02ce", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\msys2ad64ad31d3c55\\e", "ref": "msys2/cci.latest#d81a9835cf3b4b60a2ed5360e7e2630d", "remote": null, "revision_mode": "hash", "rrev": "d81a9835cf3b4b60a2ed5360e7e2630d", "rrev_timestamp": **********.195, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["msys", "unix", "subsystem"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.latest", "win_bash": null, "win_bash_run": null}, "80": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false, "tools": true}, "dependencies": {}, "deprecated": null, "description": "A C library for asynchronous DNS requests", "generators": [], "generators_folder": null, "homepage": "https://c-ares.haxx.se/", "id": "80", "immutable_package_folder": null, "info": {"options": {"shared": "False", "tools": "True"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "c-ares/1.34.5", "languages": [], "license": "MIT", "name": "c-ares", "options": {"shared": "False", "tools": "True"}, "options_definitions": {"shared": ["True", "False"], "tools": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "198f06764a7c73c097875ad130b7ca9b86884e02", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\c-are4f093deb78ae1\\e", "ref": "c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6", "remote": null, "revision_mode": "hash", "rrev": "b78b91e7cfb1f11ce777a285bbf169c6", "rrev_timestamp": **********.981, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["dns", "resolver", "async"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.34.5", "win_bash": null, "win_bash_run": null}, "81": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false, "with_icu": false}, "dependencies": {}, "deprecated": null, "description": "Fast, safe, thread-friendly regular expression library", "generators": [], "generators_folder": null, "homepage": "https://github.com/google/re2", "id": "81", "immutable_package_folder": null, "info": {"options": {"shared": "False", "with_icu": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "re2/20230301", "languages": [], "license": "BSD-3-Clause", "name": "re2", "options": {"shared": "False", "with_icu": "False"}, "options_definitions": {"shared": ["True", "False"], "with_icu": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "5c1ca629793fc7f14b78bc19617db00a4d6b3d2f", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\re21ed21f94112fb\\e", "ref": "re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4", "remote": null, "revision_mode": "hash", "rrev": "dfd6e2bf050eb90ddd8729cfb4c844a4", "rrev_timestamp": **********.773, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["regex"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "20230301", "win_bash": null, "win_bash_run": null}, "82": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "CMake, the cross-platform, open-source build system.", "generators": [], "generators_folder": null, "homepage": "https://github.com/Kitware/CMake", "id": "82", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "cmake/3.31.8", "languages": [], "license": "BSD-3-Clause", "name": "cmake", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "a0cd51c51fe9010370187244af885b0efcc5b69b", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\cmake5aa07c02504b7\\e", "ref": "cmake/3.31.8#dd6e07c418afc4b30cb1c21584dccc49", "remote": null, "revision_mode": "hash", "rrev": "dd6e07c418afc4b30cb1c21584dccc49", "rrev_timestamp": **********.571, "settings": {"arch": "armv8", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["build", "installer"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.31.8", "win_bash": null, "win_bash_run": null}, "83": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"debug_suffix": true, "fPIC": true, "lite": false, "shared": false, "upb": false, "with_rtti": true, "with_zlib": true}, "dependencies": {"84": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Protocol Buffers - Google\u0027s data interchange format", "generators": [], "generators_folder": null, "homepage": "https://github.com/protocolbuffers/protobuf", "id": "83", "immutable_package_folder": null, "info": {"options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "requires": ["zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "protobuf/3.21.12", "languages": [], "license": "BSD-3-Clause", "name": "protobuf", "options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "options_definitions": {"debug_suffix": ["True", "False"], "lite": ["True", "False"], "shared": ["True", "False"], "with_rtti": ["True", "False"], "with_zlib": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "26799874b5d54539dcdcda58519609c8a3f677e0", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\proto7649b71794a3a\\e", "ref": "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1", "remote": null, "revision_mode": "hash", "rrev": "d927114e28de9f4691a6bbcdd9a529d1", "rrev_timestamp": **********.595, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["protocol-buffers", "protocol-compiler", "serialization", "rpc", "protocol-compiler"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.21.12", "win_bash": null, "win_bash_run": null}, "84": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "A Massively Spiffy Yet Delicately Unobtrusive Compression Library (Also Free, Not to Mention Unencumbered by Patents)", "generators": [], "generators_folder": null, "homepage": "https://zlib.net", "id": "84", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "zlib/1.3.1", "languages": [], "license": "Zlib", "name": "zlib", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\zlib204752602052d\\e", "ref": "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76", "remote": null, "revision_mode": "hash", "rrev": "b8bc2603263cf7eccbd6e17e66b0ed76", "rrev_timestamp": **********.16, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["zlib", "compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.3.1", "win_bash": null, "win_bash_run": null}, "85": {"author": null, "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"addr2line_location": "/usr/bin/addr2line", "asio_no_deprecated": false, "buildid": null, "bzip2": true, "debug_level": 0, "diagnostic_definitions": false, "error_code_header_only": false, "extra_b2_flags": null, "fPIC": true, "filesystem_no_deprecated": false, "filesystem_use_std_fs": false, "filesystem_version": null, "header_only": false, "i18n_backend": "deprecated", "i18n_backend_iconv": "libc", "i18n_backend_icu": false, "layout": "system", "lzma": false, "magic_autolink": false, "multithreading": true, "namespace": "boost", "namespace_alias": false, "numa": true, "pch": true, "python_buildid": null, "python_executable": null, "python_version": null, "segmented_stacks": false, "shared": false, "system_no_deprecated": false, "system_use_utf8": false, "visibility": "hidden", "with_stacktrace_backtrace": true, "without_atomic": false, "without_charconv": false, "without_chrono": false, "without_cobalt": false, "without_container": false, "without_context": false, "without_contract": false, "without_coroutine": false, "without_date_time": false, "without_exception": false, "without_fiber": false, "without_filesystem": false, "without_graph": false, "without_graph_parallel": true, "without_iostreams": false, "without_json": false, "without_locale": false, "without_log": false, "without_math": false, "without_mpi": true, "without_nowide": false, "without_process": false, "without_program_options": false, "without_python": true, "without_random": false, "without_regex": false, "without_serialization": false, "without_stacktrace": false, "without_system": false, "without_test": false, "without_thread": false, "without_timer": false, "without_type_erasure": false, "without_url": false, "without_wave": false, "zlib": true, "zstd": false}, "dependencies": {}, "deprecated": null, "description": "Boost provides free peer-reviewed portable C++ source libraries", "generators": [], "generators_folder": null, "homepage": "https://www.boost.org", "id": "85", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "boost/1.86.0", "languages": [], "license": "BSL-1.0", "name": "boost", "options": {"asio_no_deprecated": "False", "buildid": null, "bzip2": "True", "debug_level": "0", "diagnostic_definitions": "False", "error_code_header_only": "False", "extra_b2_flags": null, "filesystem_no_deprecated": "False", "filesystem_use_std_fs": "False", "filesystem_version": null, "header_only": "True", "i18n_backend": "deprecated", "i18n_backend_iconv": "off", "i18n_backend_icu": "False", "layout": "system", "lzma": "False", "magic_autolink": "False", "multithreading": "True", "namespace": "boost", "namespace_alias": "False", "pch": "True", "python_executable": null, "python_version": null, "segmented_stacks": "False", "system_no_deprecated": "False", "system_use_utf8": "False", "visibility": "hidden", "without_atomic": "False", "without_charconv": "False", "without_chrono": "False", "without_cobalt": "True", "without_container": "False", "without_context": "False", "without_contract": "False", "without_coroutine": "False", "without_date_time": "False", "without_exception": "False", "without_fiber": "True", "without_filesystem": "False", "without_graph": "False", "without_graph_parallel": "True", "without_iostreams": "False", "without_json": "False", "without_locale": "False", "without_log": "False", "without_math": "False", "without_mpi": "True", "without_nowide": "False", "without_process": "True", "without_program_options": "False", "without_python": "True", "without_random": "False", "without_regex": "False", "without_serialization": "False", "without_stacktrace": "False", "without_system": "False", "without_test": "False", "without_thread": "False", "without_timer": "False", "without_type_erasure": "False", "without_url": "False", "without_wave": "False", "zlib": "True", "zstd": "False"}, "options_definitions": {"asio_no_deprecated": ["True", "False"], "buildid": [null, "ANY"], "bzip2": ["True", "False"], "debug_level": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"], "diagnostic_definitions": ["True", "False"], "error_code_header_only": ["True", "False"], "extra_b2_flags": [null, "ANY"], "filesystem_no_deprecated": ["True", "False"], "filesystem_use_std_fs": ["True", "False"], "filesystem_version": [null, "3", "4"], "header_only": ["True", "False"], "i18n_backend": ["iconv", "icu", null, "deprecated"], "i18n_backend_iconv": ["libc", "libiconv", "off"], "i18n_backend_icu": ["True", "False"], "layout": ["system", "versioned", "tagged"], "lzma": ["True", "False"], "magic_autolink": ["True", "False"], "multithreading": ["True", "False"], "namespace": ["ANY"], "namespace_alias": ["True", "False"], "pch": ["True", "False"], "python_executable": [null, "ANY"], "python_version": [null, "ANY"], "segmented_stacks": ["True", "False"], "system_no_deprecated": ["True", "False"], "system_use_utf8": ["True", "False"], "visibility": ["global", "protected", "hidden"], "without_atomic": ["True", "False"], "without_charconv": ["True", "False"], "without_chrono": ["True", "False"], "without_cobalt": ["True", "False"], "without_container": ["True", "False"], "without_context": ["True", "False"], "without_contract": ["True", "False"], "without_coroutine": ["True", "False"], "without_date_time": ["True", "False"], "without_exception": ["True", "False"], "without_fiber": ["True", "False"], "without_filesystem": ["True", "False"], "without_graph": ["True", "False"], "without_graph_parallel": ["True", "False"], "without_iostreams": ["True", "False"], "without_json": ["True", "False"], "without_locale": ["True", "False"], "without_log": ["True", "False"], "without_math": ["True", "False"], "without_mpi": ["True", "False"], "without_nowide": ["True", "False"], "without_process": ["True", "False"], "without_program_options": ["True", "False"], "without_python": ["True", "False"], "without_random": ["True", "False"], "without_regex": ["True", "False"], "without_serialization": ["True", "False"], "without_stacktrace": ["True", "False"], "without_system": ["True", "False"], "without_test": ["True", "False"], "without_thread": ["True", "False"], "without_timer": ["True", "False"], "without_type_erasure": ["True", "False"], "without_url": ["True", "False"], "without_wave": ["True", "False"], "zlib": ["True", "False"], "zstd": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "8932d10a88896f0bc49cde93c0045d3e", "prev_timestamp": 1750318703.498, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\boost65f2faf30eee0\\e", "ref": "boost/1.86.0#1a9d6c7521c03c76356cfec29a82acb2", "remote": null, "revision_mode": "hash", "rrev": "1a9d6c7521c03c76356cfec29a82acb2", "rrev_timestamp": 1750318700.222, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["libraries", "cpp"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.86.0", "win_bash": null, "win_bash_run": null}, "86": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"debug_suffix": true, "fPIC": true, "lite": false, "shared": false, "upb": false, "with_rtti": true, "with_zlib": true}, "dependencies": {"87": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "zlib/1.3.1", "run": false, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": true}}, "deprecated": null, "description": "Protocol Buffers - Google\u0027s data interchange format", "generators": [], "generators_folder": null, "homepage": "https://github.com/protocolbuffers/protobuf", "id": "86", "immutable_package_folder": null, "info": {"options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "requires": ["zlib/1.3.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "protobuf/3.21.12", "languages": [], "license": "BSD-3-Clause", "name": "protobuf", "options": {"debug_suffix": "True", "lite": "False", "shared": "False", "with_rtti": "True", "with_zlib": "True"}, "options_definitions": {"debug_suffix": ["True", "False"], "lite": ["True", "False"], "shared": ["True", "False"], "with_rtti": ["True", "False"], "with_zlib": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "26799874b5d54539dcdcda58519609c8a3f677e0", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\proto7649b71794a3a\\e", "ref": "protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1", "remote": null, "revision_mode": "hash", "rrev": "d927114e28de9f4691a6bbcdd9a529d1", "rrev_timestamp": **********.595, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["protocol-buffers", "protocol-compiler", "serialization", "rpc", "protocol-compiler"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "3.21.12", "win_bash": null, "win_bash_run": null}, "87": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "A Massively Spiffy Yet Delicately Unobtrusive Compression Library (Also Free, Not to Mention Unencumbered by Patents)", "generators": [], "generators_folder": null, "homepage": "https://zlib.net", "id": "87", "immutable_package_folder": null, "info": {"options": {"shared": "False"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "zlib/1.3.1", "languages": [], "license": "Zlib", "name": "zlib", "options": {"shared": "False"}, "options_definitions": {"shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "ea5dc84deb25b32a77ec23c39a1811e3f441ee54", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\zlib204752602052d\\e", "ref": "zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76", "remote": null, "revision_mode": "hash", "rrev": "b8bc2603263cf7eccbd6e17e66b0ed76", "rrev_timestamp": **********.16, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["zlib", "compression"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.3.1", "win_bash": null, "win_bash_run": null}, "88": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "stable", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "shared": false}, "dependencies": {}, "deprecated": null, "description": "Clipper is an open source freeware polygon clipping library", "generators": [], "generators_folder": null, "homepage": "http://www.angusj.com/delphi/clipper.php", "id": "88", "immutable_package_folder": null, "info": {"options": {"enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "clipper", "sentry_send_binaries": "False", "shared": "True"}, "python_requires": ["sentrylibrary/1.0.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "clipper/6.4.2@ultimaker/stable", "languages": [], "license": "BSL-1.0", "name": "clipper", "options": {"enable_sentry": "False", "sentry_create_release": "False", "sentry_project": "clipper", "sentry_send_binaries": "False", "shared": "True"}, "options_definitions": {"enable_sentry": ["True", "False"], "sentry_create_release": ["True", "False"], "sentry_project": ["ANY"], "sentry_send_binaries": ["True", "False"], "shared": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "4d9ddae5ec927ad620e4c7810767519e0997614f", "package_type": "shared-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": {"sentrylibrary/1.0.0#004cb2aaa533fb28697dd9a302d652e8": {"path": "C:\\Users\\<USER>\\.conan2\\p\\sentrb66cba1c889d9\\e", "recipe": "Cache", "remote": null}}, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\clippcc42eb1e30ea2\\e", "ref": "clipper/6.4.2@ultimaker/stable#95d9dd0b845ba2b4337f9a3fd331fa47", "remote": null, "revision_mode": "hash", "rrev": "95d9dd0b845ba2b4337f9a3fd331fa47", "rrev_timestamp": 1743751458.373, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["clipping", "polygon"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": "ultimaker", "vendor": false, "version": "6.4.2", "win_bash": null, "win_bash_run": null}, "89": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "A fast JSON parser/generator for C++ with both SAX/DOM style API", "generators": [], "generators_folder": null, "homepage": "http://rapidjson.org", "id": "89", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "rapidjson/cci.20230929", "languages": [], "license": "MIT", "name": "rapidjson", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "bc6124f3dda366933f5ac97f53b76b7b", "prev_timestamp": **********.593, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\rapidb874e80287e1b\\e", "ref": "rapidjson/cci.20230929#8dc0392af2b3aaea7312095f0ba53467", "remote": null, "revision_mode": "hash", "rrev": "8dc0392af2b3aaea7312095f0ba53467", "rrev_timestamp": **********.878, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["rapidjson", "json", "parser", "generator"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.20230929", "win_bash": null, "win_bash_run": null}, "9": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "build", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"10": {"build": true, "direct": true, "force": false, "headers": false, "libs": false, "package_id_mode": null, "ref": "msys2/cci.latest", "run": true, "skip": false, "test": false, "transitive_headers": null, "transitive_libs": null, "visible": false}}, "deprecated": null, "description": "GNU M4 is an implementation of the traditional Unix macro processor", "generators": [], "generators_folder": null, "homepage": "https://www.gnu.org/software/m4/", "id": "9", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "build_type": "Release", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "m4/1.4.19", "languages": [], "license": "GPL-3.0-only", "name": "m4", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "346b6d9d305b7368090620c373efae6fb71604db", "package_type": "application", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\m4512cc8aabbc4c\\e", "ref": "m4/1.4.19#b38ced39a01e31fef5435bc634461fd2", "remote": null, "revision_mode": "hash", "rrev": "b38ced39a01e31fef5435bc634461fd2", "rrev_timestamp": **********.063, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["macro", "preprocessor"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.4.19", "win_bash": true, "win_bash_run": null}, "90": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"with_deprecated": true}, "dependencies": {}, "deprecated": null, "description": "single-file public domain libraries for C/C++", "generators": [], "generators_folder": null, "homepage": "https://github.com/nothings/stb", "id": "90", "immutable_package_folder": null, "info": {"options": {"with_deprecated": "True"}}, "info_invalid": null, "invalid_build": false, "label": "stb/cci.20230920", "languages": [], "license": ["Unlicense", "MIT"], "name": "stb", "options": {"with_deprecated": "True"}, "options_definitions": {"with_deprecated": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "24b381c7532f70c284a2a67cc83f779b3c0042fb", "package_type": "header-library", "prev": "72af46794853f74751a3ba685e1c7ef4", "prev_timestamp": **********.818, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\stbdab76fef840b0\\e", "ref": "stb/cci.20230920#ed79bd361e974a99137f214efb117eef", "remote": null, "revision_mode": "hash", "rrev": "ed79bd361e974a99137f214efb117eef", "rrev_timestamp": **********.481, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["stb", "single-file", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "cci.20230920", "win_bash": null, "win_bash_run": null}, "91": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "header_only": false, "no_exceptions": false, "shared": false, "use_std_fmt": false, "wchar_console": false, "wchar_filenames": false, "wchar_support": false}, "dependencies": {"92": {"build": false, "direct": true, "force": false, "headers": true, "libs": true, "package_id_mode": "minor_mode", "ref": "fmt/11.1.3", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}}, "deprecated": null, "description": "Fast C++ logging library", "generators": [], "generators_folder": null, "homepage": "https://github.com/gabime/spdlog", "id": "91", "immutable_package_folder": null, "info": {"options": {"header_only": "False", "no_exceptions": "False", "shared": "False", "use_std_fmt": "False", "wchar_console": "False", "wchar_filenames": "False", "wchar_support": "False"}, "requires": ["fmt/11.1.Z"], "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "spdlog/1.15.1", "languages": [], "license": "MIT", "name": "spdlog", "options": {"header_only": "False", "no_exceptions": "False", "shared": "False", "use_std_fmt": "False", "wchar_console": "False", "wchar_filenames": "False", "wchar_support": "False"}, "options_definitions": {"header_only": ["True", "False"], "no_exceptions": ["True", "False"], "shared": ["True", "False"], "use_std_fmt": ["True", "False"], "wchar_console": ["True", "False"], "wchar_filenames": ["True", "False"], "wchar_support": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "87c963d0e391a1419624873329f5ab4cc8db0cf8", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\spdlo1df5a164d5490\\e", "ref": "spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7", "remote": null, "revision_mode": "hash", "rrev": "92e99f07f134481bce4b70c1a41060e7", "rrev_timestamp": **********.122, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["logger", "logging", "log-filtering", "file sink", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.15.1", "win_bash": null, "win_bash_run": null}, "92": {"author": null, "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": {"fPIC": true, "header_only": false, "shared": false, "with_fmt_alias": false, "with_os_api": true, "with_unicode": true}, "dependencies": {}, "deprecated": null, "description": "A safe and fast alternative to printf and IOStreams.", "generators": [], "generators_folder": null, "homepage": "https://github.com/fmtlib/fmt", "id": "92", "immutable_package_folder": null, "info": {"options": {"header_only": "False", "shared": "False", "with_os_api": "True", "with_unicode": "True"}, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "fmt/11.1.3", "languages": [], "license": "MIT", "name": "fmt", "options": {"header_only": "False", "shared": "False", "with_fmt_alias": "False", "with_os_api": "True", "with_unicode": "True"}, "options_definitions": {"header_only": ["True", "False"], "shared": ["True", "False"], "with_fmt_alias": ["True", "False"], "with_os_api": ["True", "False"], "with_unicode": ["True", "False"]}, "options_description": null, "package_folder": null, "package_id": "3d87232490906d668db42ba1c776b65f7f328833", "package_type": "static-library", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\fmt4d7205ea09cf5\\e", "ref": "fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae", "remote": null, "revision_mode": "hash", "rrev": "8364f0feb23ee32e4b870455edb552ae", "rrev_timestamp": **********.718, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["format", "iostream", "printf"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "11.1.3", "win_bash": null, "win_bash_run": null}, "93": {"author": null, "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Range library for C++14/17/20, basis for C++20\u0027s std::ranges", "generators": [], "generators_folder": null, "homepage": "https://github.com/ericniebler/range-v3", "id": "93", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "range-v3/0.12.0", "languages": [], "license": "BSL-1.0", "name": "range-v3", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "ecc6172c3cd6694c36d1cd98a702deb0", "prev_timestamp": **********.471, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\range81ef55d99829c\\e", "ref": "range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408", "remote": null, "revision_mode": "hash", "rrev": "4c05d91d7b40e6b91b44b5345ac64408", "rrev_timestamp": **********.379, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["range", "range-library", "proposal", "iterator", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "0.12.0", "win_bash": null, "win_bash_run": null}, "94": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "stable", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"95": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "unrelated_mode", "ref": "mapbox-geometry/2.0.3", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}, "96": {"build": false, "direct": false, "force": false, "headers": true, "libs": false, "package_id_mode": "unrelated_mode", "ref": "mapbox-variant/1.2.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}}, "deprecated": null, "description": "A general library for geometry operations of union, intersections, difference, and xor", "generators": [], "generators_folder": null, "homepage": "https://github.com/mapbox/wagyu/", "id": "94", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "mapbox-wagyu/0.5.0@ultimaker/stable", "languages": [], "license": "LicenseRef-mapbox-wagyu", "name": "mapbox-wagyu", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "9c99eaae4d7ee99d627e4533142e2336", "prev_timestamp": **********.378, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\mapbo772d3ce9de7e4\\e", "ref": "mapbox-wagyu/0.5.0@ultimaker/stable#********************************", "remote": null, "revision_mode": "hash", "rrev": "********************************", "rrev_timestamp": **********.744, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["geometry", "clipping", "header-only"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": "ultimaker", "vendor": false, "version": "0.5.0", "win_bash": null, "win_bash_run": null}, "95": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {"96": {"build": false, "direct": true, "force": false, "headers": true, "libs": false, "package_id_mode": "unrelated_mode", "ref": "mapbox-variant/1.2.0", "run": false, "skip": false, "test": false, "transitive_headers": true, "transitive_libs": true, "visible": true}}, "deprecated": null, "description": "Provides header-only, generic C++ interfaces for geometry types, geometry collections, and features.", "generators": [], "generators_folder": null, "homepage": "https://github.com/mapbox/geometry.hpp", "id": "95", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "mapbox-geometry/2.0.3", "languages": [], "license": "ISC", "name": "mapbox-geometry", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "b2b6166a216462f17c02e1c4bebbd942", "prev_timestamp": **********.953, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\mapbod90ac45e6a9f3\\e", "ref": "mapbox-geometry/2.0.3#********************************", "remote": null, "revision_mode": "hash", "rrev": "********************************", "rrev_timestamp": **********.929, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["geometry"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "2.0.3", "win_bash": null, "win_bash_run": null}, "96": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "An header-only alternative to boost::variant for C++11 and C++14", "generators": [], "generators_folder": null, "homepage": "https://github.com/mapbox/variant", "id": "96", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "mapbox-variant/1.2.0", "languages": [], "license": "BSD-3-Clause", "name": "mapbox-variant", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "header-library", "prev": "3526db39b7b7a80e939cc264b9df29c6", "prev_timestamp": **********.185, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\mapbo1453b0c426c4d\\e", "ref": "mapbox-variant/1.2.0#********************************", "remote": null, "revision_mode": "hash", "rrev": "********************************", "rrev_timestamp": **********.211, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["variant"], "upload_policy": null, "url": "https://github.com/conan-io/conan-center-index", "user": null, "vendor": false, "version": "1.2.0", "win_bash": null, "win_bash_run": null}, "97": {"author": null, "binary": "Skip", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": null, "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": null, "generators": [], "generators_folder": null, "homepage": null, "id": "97", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "standardprojectsettings/0.2.0", "languages": [], "license": null, "name": "standardprojectsettings", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "build-scripts", "prev": "11a0ba14a3eaa94059396f4af59df2c5", "prev_timestamp": **********.139, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\standb8d7e3fc38179\\e", "ref": "standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365", "remote": null, "revision_mode": "hash", "rrev": "af70631ee980187032a76e136b293365", "rrev_timestamp": **********.884, "settings": {}, "source_folder": null, "system_requires": {}, "test": true, "topics": null, "upload_policy": null, "url": null, "user": null, "vendor": false, "version": "0.2.0", "win_bash": null, "win_bash_run": null}, "98": {"author": "Ultimaker B.V.", "binary": "Missing", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": null, "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": ["resources\\cura", "resources\\uranium", "windows"], "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "Contains binary data for Cura releases, like compiled translations and firmware.", "generators": [], "generators_folder": null, "homepage": null, "id": "98", "immutable_package_folder": null, "info": {"settings": {"arch": "armv8", "os": "Windows"}}, "info_invalid": null, "invalid_build": false, "label": "cura_binary_data/5.11.0-alpha.0@ultimaker/testing", "languages": [], "license": "LGPL-3.0", "name": "cura_binary_data", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "d89739f352eb696e1cfe06a83c405deeb0ef2985", "package_type": "unknown", "prev": null, "prev_timestamp": null, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\cura_00d2321ee88c9\\e", "ref": "cura_binary_data/5.11.0-alpha.0@ultimaker/testing#760976726c555ed281aa0b64cdf893db", "remote": null, "revision_mode": "hash", "rrev": "760976726c555ed281aa0b64cdf893db", "rrev_timestamp": **********.399, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "binaries", "translation", "firmware", "cura"], "upload_policy": null, "url": "https://github.com/Ultimaker/cura-binary-data", "user": "ultimaker", "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}, "99": {"author": "Ultimaker B.V.", "binary": "Cache", "binary_remote": null, "build_folder": null, "build_id": null, "build_policy": "missing", "channel": "testing", "conf_info": {}, "context": "host", "cpp_info": {"root": {"bindirs": ["bin"], "builddirs": null, "cflags": null, "cxxflags": null, "defines": null, "exelinkflags": null, "frameworkdirs": null, "frameworks": null, "includedirs": ["include"], "libdirs": ["lib"], "libs": null, "objects": null, "properties": null, "requires": null, "resdirs": null, "sharedlinkflags": null, "srcdirs": null, "sysroot": null, "system_libs": null}}, "default_options": null, "dependencies": {}, "deprecated": null, "description": "FDM Material database", "generators": [], "generators_folder": null, "homepage": null, "id": "99", "immutable_package_folder": null, "info": {}, "info_invalid": null, "invalid_build": false, "label": "fdm_materials/5.11.0-alpha.0@ultimaker/testing", "languages": [], "license": "LGPL-3.0", "name": "fdm_materials", "options": {}, "options_definitions": {}, "options_description": null, "package_folder": null, "package_id": "da39a3ee5e6b4b0d3255bfef95601890afd80709", "package_type": "unknown", "prev": "a804b57beecad14bcd83016beb2d1f66", "prev_timestamp": **********.301881, "provides": null, "python_requires": null, "recipe": "Cache", "recipe_folder": "C:\\Users\\<USER>\\.conan2\\p\\fdm_med39c632d7050\\e", "ref": "fdm_materials/5.11.0-alpha.0@ultimaker/testing#b12456ad8f9d9464e1d5db569cacef3c", "remote": null, "revision_mode": "hash", "rrev": "b12456ad8f9d9464e1d5db569cacef3c", "rrev_timestamp": **********.501, "settings": {"arch": "armv8", "build_type": "Release", "compiler": "msvc", "compiler.cppstd": "17", "compiler.runtime": "dynamic", "compiler.runtime_type": "Release", "compiler.version": "193", "os": "Windows"}, "source_folder": null, "system_requires": {}, "test": false, "topics": ["conan", "profiles", "cura", "ultimaker", "filament"], "upload_policy": null, "url": "https://github.com/Ultimaker/fdm_materials", "user": "ultimaker", "vendor": false, "version": "5.11.0-alpha.0", "win_bash": null, "win_bash_run": null}}, "overrides": {"grpc/1.67.1": ["grpc/1.54.3"]}, "replaced_requires": {}, "resolved_ranges": {"abseil/[\u003e=20230125.3 \u003c=20230802.1]": "abseil/20230802.1", "c-ares/[\u003e=1.19.1 \u003c2]": "c-ares/1.34.5", "cmake/[\u003e=3.25 \u003c4]": "cmake/3.31.8", "expat/[\u003e=2.6.2 \u003c3]": "expat/2.7.1", "npmpackage/[\u003e=1.0.0]": "npmpackage/1.1.0", "openssl/[\u003e=1.1 \u003c4]": "openssl/3.5.0", "pyprojecttoolchain/[\u003e=0.2.0]": "pyprojecttoolchain/0.2.0", "scripta/[\u003e=1.1.0]@ultimaker/testing": "scripta/1.1.0-alpha.0+b96045@ultimaker/testing", "sipbuildtool/[\u003e=0.3.0]": "sipbuildtool/0.3.0", "standardprojectsettings/[\u003e=0.1.0]": "standardprojectsettings/0.2.0", "standardprojectsettings/[\u003e=0.2.0]": "standardprojectsettings/0.2.0", "standardprojectsettings/[\u003e=0.2.0]@ultimaker/stable": "standardprojectsettings/0.2.0@ultimaker/stable", "translationextractor/[\u003e=2.2.0]": "translationextractor/2.3.0", "zlib/[\u003e=1.2.11 \u003c2]": "zlib/1.3.1"}, "root": {"0": "cura/5.11.0-alpha.0"}};
            let hide_build = false;
            let hide_test = false;
            let search_pkgs = null;
            let excluded_pkgs = null;
            let collapse_packages = false;
            let show_package_type = false;
            let color_map = {Cache: "SkyBlue",
                             Download: "LightGreen",
                             Build: "Yellow",
                             Missing: "Orange",
                             Update: "SeaGreen",
                             Skip: "White",
                             Editable: "LightCyan",
                             EditableBuild: "Cyan",
                             Invalid: "Red",
                             Platform: "Violet"};
            let global_edges = {};
            function define_data(){
                let nodes = [];
                let edges = [];
                let collapsed_packages = {};
                let targets = {};
                global_edges = {};
                let edge_counter = 0;
                let conflict=null;
                if (graph_data["error"] && graph_data["error"]["type"] == "conflict")
                    conflict = graph_data["error"];
                for (const [node_id, node] of Object.entries(graph_data["nodes"])) {
                    if (node.context == "build" && hide_build) continue;
                    if (node.test && hide_test) continue;
                    let shape = node.context == "build" || node.test ? "ellipse" : "box";
                    let label = null;
                    if (node["name"])
                        label =  node["name"] + "/" + node["version"];
                    else if (node["ref"])
                        label = node["ref"];
                    else
                        label = node.recipe == "Consumer"? "conanfile": "CLI";
                    if (collapse_packages) {
                        let existing = collapsed_packages[label];
                        targets[node_id] = existing;
                        if (existing) continue;
                        collapsed_packages[label] = node_id;
                    }
                    if (excluded_pkgs) {
                        let patterns = excluded_pkgs.split(',')
                            .map(pattern => pattern.trim())
                            .filter(pattern => pattern.length > 0)
                            .map(pattern => pattern.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
                        if (patterns.some(pattern => label.match(pattern))) {
                            continue;
                        }
                    }
                    if (show_package_type) {
                         label = "<b>" + label + "\n" + "<i>" + node.package_type + "</i>";
                    }
                    borderWidth = 1;
                    borderColor = "SkyBlue";
                    font = {multi: 'html'};
                    shapeProperties = {};
                    let color = color_map[node.binary]
                    if (conflict && conflict.branch1.dst_id == node_id){
                        font.color = "white";
                        color = "Black";
                        shape = "circle";
                    }
                    if (search_pkgs) {
                        let patterns = search_pkgs.split(',')
                            .map(pattern => pattern.trim())
                            .filter(pattern => pattern.length > 0)
                            .map(pattern => pattern.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
                        if (patterns.some(pattern => label.match(pattern))) {
                            borderWidth = 3;
                            borderColor = "Magenta";
                        }
                    }
                    if (node.test) {
                        font.background = "lightgrey";
                        shapeProperties = {borderDashes: true};
                    }
                    if (node.recipe == "Platform") {
                        font.background = "Violet";
                    }
                    if (node.vendor) {
                        borderColor = "Red";
                        shapeProperties = {borderDashes: [3,5]};
                        borderWidth = 2;
                    }
                    nodes.push({
                        id: node_id,
                        font: font,
                        label: label,
                        shape: shape,
                        shapeProperties: shapeProperties,
                        borderWidth: borderWidth,
                        color: {border: borderColor, background: color,
                                highlight: {background: color, border: "Blue"}},
                    });
                }
                for (const [node_id, node] of Object.entries(graph_data["nodes"])) {
                    for (const [dep_id, dep] of Object.entries(node["dependencies"])) {
                        if (dep.direct){
                            let target_id = targets[dep_id] || dep_id;
                            edges.push({id: edge_counter, from: node_id, to: target_id,
                                        color: {color: "SkyBlue", highlight: "Blue"}});
                            global_edges[edge_counter++] = dep;
                        }
                    }
                }
                if (conflict) {
                    let conflict_id = null;
                    if (conflict.branch1.dst_id) { // already created conflict node
                        conflict_id = conflict.branch1.dst_id;
                    }
                    else {
                        conflict_id = "conflict_id";
                        nodes.push({
                            id: conflict_id,
                            font: {color: "white"},
                            label: conflict.name,
                            shape: "circle",
                            color: {background: "black",
                                    highlight: {background: "black", border: "Blue"}},
                        });
                        edges.push({id: edge_counter, from: conflict.branch1.src_id, to: conflict_id,
                                    color: {color: "Red", highlight: "Red"},
                                    label: conflict.branch1.require.ref});
                        global_edges[edge_counter++] = conflict.branch1.require;
                    }
                    edges.push({id: edge_counter, from: conflict.branch2.src_id, to: conflict_id,
                                color: {color: "Red", highlight: "Red"},
                                label: conflict.branch2.require.ref});
                    global_edges[edge_counter++] = conflict.branch2.require;
                }
                return {nodes: new vis.DataSet(nodes), edges: new vis.DataSet(edges)};
            };
            function define_legend() {
                let x = 0;
                let y = 0;
                let step = 250;
                let legend_nodes = [];
                legend_nodes.push({id: 0, x: x, y: y, shape: "box", font: {size: 35},
                    label: "require",
                });
                legend_nodes.push({id: 1, x: x + step, y: y, font: {size: 35}, shape: "ellipse",
                    label: "tool-require",
                });
                legend_nodes.push({id: 2, x: x + 2* step, y: y, font: {size: 35, background: "lightgrey"},
                    shape: "ellipse", shapeProperties: {borderDashes: true},
                    label: "test-require",
                })
                let counter = 3;
                legend_nodes.push({x: x + counter*step, y: y, shape: "ellipse",
                    label: "platform",
                    font: {size: 35, background: "Violet"},
                });
                counter++;
                for (const [status, color] of Object.entries(color_map)) {
                    legend_nodes.push({x: x + counter*step, y: y, shape: "box", font: {size: 35},
                        label: status,
                        color: {border: "SkyBlue", background: color}
                    });
                    counter++;
                }
                legend_nodes.push({x: x + counter*step, y: y, shape: "box",
                    label: "conflict",
                    font: {size: 35, color: "white"},
                    color: {border: "SkyBlue", background: "Black"}
                });
                counter++;

                legend_nodes.push({x: x + counter*step, y: y, shape: "box",
                    label: "vendor", font: {size: 35},
                    color: {border: "Red"},
                    shapeProperties: {borderDashes: [3,5]},
                    borderWidth: 2
                });
                return {nodes: new vis.DataSet(legend_nodes)};
            }
            let error = document.getElementById("error");
            if (graph_data["error"]){
                 let div = document.createElement('div');
                 div.innerHTML = "<pre>Error in the graph: " + JSON.stringify(graph_data["error"], undefined, 2) + "</pre>";
                 error.appendChild(div);
            }
            let container = document.getElementById('mynetwork');
            let controls = document.getElementById('controls');
            let legend_container = document.getElementById('mylegend');

            let options = {
                autoResize: true,
                locale: 'en',
                edges: {
                    arrows: { to: {enabled: true} },
                    smooth: { enabled: false}
                },
                nodes: {font: {'face': 'monospace', 'align': 'left'}},
                layout: {
                    "hierarchical": {
                        enabled: true,
                        sortMethod: "directed",
                        direction: "DU",
                        nodeSpacing: 170,
                        blockShifting: true,
                        edgeMinimization: true,
                        shakeTowards: "roots",
                    }
                },
                physics: { enabled: false},
                configure: {
                    enabled: true,
                    filter: 'layout physics',
                    showButton: false,
                    container: controls
                }
            };

            let data = define_data();
            let network = new vis.Network(container, data, options);
            let legend_data = define_legend();
            let options_legend = {interaction: {selectable: false, dragView: false, dragNodes: false,
                                                zoomView: false}, physics: {enabled: false}};
            let legend = new vis.Network(legend_container, legend_data, options_legend);

            network.on('click', function (properties) {
                let ids = properties.nodes;
                let ids_edges = properties.edges;
                let control = document.getElementById("details");
                while (control.firstChild) {
                    control.removeChild(control.firstChild);
                }
                if(ids[0] || ids_edges[0]) {
                    selected = graph_data["nodes"][ids[0]] || global_edges[ids_edges[0]];
                    let div = document.createElement('div');
                    let f = Object.fromEntries(Object.entries(selected).filter(([_, v]) => v != null));
                    div.innerText = JSON.stringify(f, undefined, 2);
                    let div2 = document.createElement('div');
                    div2.innerHTML = "<pre>" + div.innerHTML + "</pre>";
                    control.appendChild(div2);
                }
                else {
                    control.innerHTML = "<b>Info</b>: Click on a package or edge for more info";
                }
            });
            function draw() {
                let scale = network.getScale();
                let viewPos = network.getViewPosition();
                data = define_data();
                network.setData(data);
                network.redraw();
                network.moveTo({position: viewPos, scale: scale});
            }
            function switchBuild() {
                hide_build = !hide_build;
                draw();
            }
            function switchTest() {
                hide_test = !hide_test;
                draw();
            }
            function collapsePackages() {
                collapse_packages = !collapse_packages;
                draw();
            }
            function searchPackages(e) {
                search_pkgs = e.value;
                draw();
            }
            function excludePackages(e) {
                excluded_pkgs = e.value;
                draw();
            }
            function showPackageType(e) {
                show_package_type = !show_package_type;
                draw();
            }
            function showhideclass(id) {
                let elements = document.getElementsByClassName(id)
                for (let i = 0; i < elements.length; i++) {
                    elements[i].style.display = (elements[i].style.display != 'none') ? 'none' : 'block';
                }
            }
            window.addEventListener("load", () => {
               draw();
            });
        </script>
    </body>
</html>
