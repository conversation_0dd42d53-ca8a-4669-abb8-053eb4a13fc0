[general]
definition = ultimaker3_extended
name = BB 0.8
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
acceleration_enabled = True
acceleration_prime_tower = =math.ceil(acceleration_print * 200 / 3500)
acceleration_support = =math.ceil(acceleration_print * 2000 / 3500)
acceleration_support_bottom = =math.ceil(acceleration_support_interface * 100 / 1500)
acceleration_support_interface = =math.ceil(acceleration_support * 1500 / 2000)
brim_width = 3
cool_fan_speed = 50
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
infill_wipe_dist = 0
jerk_print = 25
layer_height = 0.2
machine_min_cool_heat_time_window = 15
machine_nozzle_heat_up_speed = 1.5
machine_nozzle_id = BB 0.8
machine_nozzle_size = 0.8
machine_nozzle_tip_outer_diameter = 2.0
multiple_mesh_overlap = 0
prime_tower_enable = False
prime_tower_wipe_enabled = True
raft_surface_layers = 1
retraction_hop = 2
retraction_hop_only_when_collides = True
retraction_min_travel = =line_width * 3
retraction_prime_speed = 15
skin_overlap = 5
speed_prime_tower = =math.ceil(speed_print * 7 / 35)
speed_print = 35
speed_support = =math.ceil(speed_print * 25 / 35)
speed_support_interface = =math.ceil(speed_support * 20 / 25)
speed_wall_0 = =math.ceil(speed_wall * 25 / 30)
support_angle = 60
support_bottom_height = =layer_height * 2
support_interface_enable = True
support_z_distance = 0
switch_extruder_prime_speed = 15
switch_extruder_retraction_amount = 12
top_bottom_thickness = 1
wall_0_inset = 0

