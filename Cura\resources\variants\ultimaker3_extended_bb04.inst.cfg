[general]
definition = ultimaker3_extended
name = BB 0.4
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
acceleration_prime_tower = =math.ceil(acceleration_print * 200 / 3500)
acceleration_support = =math.ceil(acceleration_print * 2000 / 3500)
acceleration_support_bottom = =math.ceil(acceleration_support_interface * 100 / 1500)
acceleration_support_interface = =math.ceil(acceleration_support * 1500 / 2000)
machine_nozzle_heat_up_speed = 1.5
machine_nozzle_id = BB 0.4
machine_nozzle_tip_outer_diameter = 1.0
retraction_min_travel = =3 * line_width
speed_prime_tower = =math.ceil(speed_print * 10 / 35)
speed_support = =math.ceil(speed_print * 25 / 35)
speed_support_interface = =math.ceil(speed_support * 20 / 25)
speed_wall_0 = =math.ceil(speed_wall * 25 / 30)
support_bottom_height = =layer_height * 2
support_interface_enable = True
switch_extruder_retraction_amount = 12

