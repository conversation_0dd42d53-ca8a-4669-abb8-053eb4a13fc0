[general]
definition = ultimaker_s7
name = AA 0.8
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
brim_width = 7
cool_fan_speed = 7
default_material_print_temperature = 200
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
infill_wipe_dist = 0
jerk_print = 25
layer_height = 0.2
machine_min_cool_heat_time_window = 15
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
machine_nozzle_id = AA 0.8
machine_nozzle_size = 0.8
machine_nozzle_tip_outer_diameter = 2.0
multiple_mesh_overlap = 0
prime_tower_enable = False
prime_tower_wipe_enabled = True
raft_surface_layers = 1
retraction_amount = 6.5
retraction_hop = 2
retraction_hop_only_when_collides = True
retraction_speed = 25
speed_print = 35
speed_topbottom = =math.ceil(speed_print * 25 / 35)
speed_wall_0 = =math.ceil(speed_wall * 25 / 30)
support_angle = 60
support_bottom_distance = =support_z_distance / 2
support_top_distance = =support_z_distance
support_z_distance = =layer_height * 2
switch_extruder_prime_speed = 20
switch_extruder_retraction_amount = 16.5
top_bottom_thickness = 1.4
wall_0_inset = 0

